<?php
/**
 * Main Configuration File for ISHTEEAHAR Digital Agency
 *
 * This file contains site-wide configuration settings and constants
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Development/Production Mode
define('DEVELOPMENT_MODE', true); // Set to false in production

// Error Reporting
if (DEVELOPMENT_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Site Configuration
define('SITE_NAME', 'ISHTEEAHAR');
define('SITE_TAGLINE', 'Digital Agency Excellence');
define('SITE_URL', 'http://localhost/first'); // Change this to your domain
define('SITE_EMAIL', '<EMAIL>');

// File Paths
define('ROOT_PATH', dirname(dirname(__FILE__)));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// URL Paths
define('BASE_URL', SITE_URL);
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');
define('CSS_URL', BASE_URL . '/css');
define('JS_URL', BASE_URL . '/js');
define('IMAGES_URL', BASE_URL . '/images');

// Database Configuration
if (file_exists(CONFIG_PATH . '/database.php')) {
    require_once CONFIG_PATH . '/database.php';
} else {
    // Fallback if database config doesn't exist
    function getDbConnection() {
        throw new Exception('Database configuration not found');
    }
}

// Timezone
date_default_timezone_set('UTC');

// Security Settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour

// Pagination
define('ITEMS_PER_PAGE', 12);
define('PORTFOLIO_PER_PAGE', 9);

// File Upload Settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'txt']);

// Email Configuration
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_ENCRYPTION', 'tls');

// Social Media Links (will be loaded from database)
$social_links = [];

// Site Settings Cache
$site_settings = [];

/**
 * Load site settings from database
 */
function loadSiteSettings() {
    global $site_settings;

    try {
        if (function_exists('fetchAll')) {
            $settings = fetchAll("SELECT setting_name, setting_value FROM site_settings WHERE 1");
            foreach ($settings as $setting) {
                $site_settings[$setting['setting_name']] = $setting['setting_value'];
            }
        } else {
            throw new Exception('Database functions not available');
        }
    } catch (Exception $e) {
        // If database is not available, use defaults
        $site_settings = [
            'site_title' => SITE_NAME,
            'site_tagline' => SITE_TAGLINE,
            'company_email' => SITE_EMAIL,
            'company_phone' => '+****************',
            'hero_title' => 'ISHTEEAHAR',
            'hero_subtitle' => 'We Are A Premier Digital Agency Delivering Excellence In Web Development, Digital Marketing & Creative Solutions',
            'about_title' => 'About ISHTEEAHAR',
            'about_description' => 'We are a full-service digital agency committed to delivering exceptional results for our clients. With expertise spanning web development, digital marketing, and creative services, we transform ideas into powerful digital experiences that drive business growth.',
            'company_name' => 'ISHTEEAHAR Digital Agency',
            'company_address' => '123 Digital Street, Tech City, TC 12345',
            'facebook_url' => '#',
            'twitter_url' => '#',
            'instagram_url' => '#',
            'linkedin_url' => '#',
            'youtube_url' => '#'
        ];
    }
}

/**
 * Get site setting value
 *
 * @param string $key Setting key
 * @param string $default Default value if setting not found
 * @return string Setting value
 */
function getSetting($key, $default = '') {
    global $site_settings;
    return isset($site_settings[$key]) ? $site_settings[$key] : $default;
}

/**
 * Generate CSRF token
 *
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 *
 * @param string $token Token to verify
 * @return bool True if valid
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 *
 * @param mixed $data Input data
 * @return mixed Sanitized data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Generate slug from string
 *
 * @param string $string Input string
 * @return string URL-friendly slug
 */
function generateSlug($string) {
    $slug = strtolower(trim($string));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    return trim($slug, '-');
}

/**
 * Format date for display
 *
 * @param string $date Date string
 * @param string $format Date format
 * @return string Formatted date
 */
function formatDate($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

/**
 * Truncate text to specified length
 *
 * @param string $text Text to truncate
 * @param int $length Maximum length
 * @param string $suffix Suffix to add
 * @return string Truncated text
 */
function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    return substr($text, 0, $length) . $suffix;
}

/**
 * Check if current page is active
 *
 * @param string $page Page name
 * @return string 'active' if current page
 */
function isActivePage($page) {
    $current_page = basename($_SERVER['PHP_SELF'], '.php');
    return ($current_page === $page) ? 'active' : '';
}

/**
 * Redirect to URL
 *
 * @param string $url URL to redirect to
 * @param int $status_code HTTP status code
 */
function redirect($url, $status_code = 302) {
    header("Location: $url", true, $status_code);
    exit();
}

/**
 * Get current URL
 *
 * @return string Current URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $protocol . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

// Load site settings
loadSiteSettings();

// Auto-load common functions
require_once INCLUDES_PATH . '/functions.php';
?>
