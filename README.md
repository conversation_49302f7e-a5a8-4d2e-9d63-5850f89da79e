# ISHTEEAHAR Digital Agency Website

A professional PHP & MySQL-based digital agency website featuring modern design, 3D effects, and comprehensive service management.

## Features

### ✅ **Main Pages**
- **Home** - Hero section with animated elements and service overview
- **About** - Company information, mission, vision, and team
- **Services** - Comprehensive service listings with detailed descriptions
- **Portfolio** - Project showcase with filtering by service category
- **Contact** - Contact form with database storage and email notifications

### ✅ **Services (6 Total)**
Each service includes:
- Service description and features
- "View Portfolio" button linking to service-specific portfolio
- Professional content and modern presentation

**Service List:**
1. **Web Development** - Custom websites and web applications
2. **Digital Marketing** - SEO, PPC, and online marketing strategies
3. **WordPress Development** - Custom themes and plugin development
4. **Graphic Designing / Artwork** - Brand identity and visual design
5. **Video Editing** - Professional video production and editing
6. **Social Media Marketing / Management** - Complete social media solutions

### ✅ **Design Features**
- **Black Theme** with elegant, professional layout
- **3D Text Effects** and visual elements
- **Animations** - Scroll, fade, and zoom effects
- **Responsive Design** - Mobile-friendly across all devices
- **Modern UI/UX** - Clean, professional interface

### ✅ **Technical Features**
- **PHP & MySQL** backend with secure database operations
- **Dynamic Content Management** - Easy content updates
- **Contact Form** with database storage and email notifications
- **Portfolio Management** - Organized by service categories
- **SEO Optimized** - Meta tags and structured content
- **Security Features** - CSRF protection and input sanitization

## Installation & Setup

### Prerequisites
- **Web Server** (Apache/Nginx)
- **PHP 7.4+** with PDO MySQL extension
- **MySQL 5.7+** or MariaDB
- **XAMPP/WAMP/LAMP** for local development

### Step 1: Database Setup
1. Create a new MySQL database named `ishteeahar_agency`
2. Import the database structure and sample data:
   ```sql
   mysql -u root -p ishteeahar_agency < database.sql
   ```

### Step 2: Configuration
1. Update database credentials in `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'ishteeahar_agency');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

2. Update site settings in `config/config.php`:
   ```php
   define('SITE_URL', 'http://your-domain.com');
   define('SITE_EMAIL', '<EMAIL>');
   ```

3. Update email recipient in `php/send-email.php`:
   ```php
   $to = getSetting('company_email', '<EMAIL>');
   ```

### Step 3: File Permissions
Set appropriate permissions for directories:
```bash
chmod 755 uploads/
chmod 755 logs/
chmod 644 config/*.php
```

### Step 4: Testing
1. Access your website: `http://your-domain.com/index.php`
2. Test contact form functionality
3. Verify database connections
4. Check all navigation links

## File Structure

```
/
├── config/
│   ├── database.php      # Database configuration
│   └── config.php        # Main configuration
├── includes/
│   ├── functions.php     # Common functions
│   ├── header.php        # Header template
│   └── footer.php        # Footer template
├── php/
│   └── send-email.php    # Contact form handler
├── css/                  # Stylesheets
├── js/                   # JavaScript files
├── images/               # Image assets
├── index.php             # Homepage
├── about.php             # About page
├── services.php          # Services page
├── portfolio.php         # Portfolio page
├── contact.php           # Contact page
├── database.sql          # Database setup file
└── README.md             # This file
```

## Customization

### Adding New Services
1. Insert into `services` table via database
2. Add corresponding portfolio items
3. Update service icons in the database

### Updating Content
- **Site Settings**: Update via `site_settings` table
- **Services**: Modify `services` table
- **Portfolio**: Add/edit `portfolio_items` table
- **Contact Info**: Update in `site_settings` table

### Styling Customization
- **Main Styles**: `css/style.css`
- **Custom Styles**: Added in `includes/header.php`
- **3D Effects**: Defined in header template
- **Animations**: AOS library + custom CSS

## Database Schema

### Tables
- **services** - Service information and descriptions
- **portfolio_items** - Project portfolio with service relationships
- **contact_messages** - Contact form submissions
- **site_settings** - Configurable site settings

### Key Relationships
- Portfolio items linked to services via `service_id`
- Settings stored as key-value pairs for easy management

## Security Features

- **CSRF Protection** - Token-based form security
- **Input Sanitization** - All user inputs cleaned
- **SQL Injection Prevention** - Prepared statements
- **XSS Protection** - Output escaping
- **Error Logging** - Secure error handling

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Responsive**: iOS Safari, Chrome Mobile
- **Graceful Degradation**: Older browser support

## Performance Features

- **Optimized CSS/JS** - Minified vendor files
- **Image Optimization** - Placeholder system ready
- **Database Optimization** - Indexed queries
- **Caching Ready** - Structure supports caching

## Support & Maintenance

### Regular Tasks
- **Database Backups** - Regular automated backups
- **Security Updates** - Keep PHP and dependencies updated
- **Content Updates** - Regular portfolio and service updates
- **Performance Monitoring** - Monitor site speed and uptime

### Troubleshooting
- Check error logs in `/logs/` directory
- Verify database connections
- Ensure proper file permissions
- Test contact form functionality

## License

This project is created for ISHTEEAHAR Digital Agency. All rights reserved.

## Contact

For technical support or customization requests, please contact the development team.

---

**ISHTEEAHAR Digital Agency** - Excellence in Digital Solutions
