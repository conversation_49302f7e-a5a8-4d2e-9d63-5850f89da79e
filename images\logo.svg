<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffd700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="40" r="35" fill="none" stroke="#ffd700" stroke-width="2" opacity="0.3"/>
  
  <!-- Main text -->
  <text x="100" y="45" font-family="Arial, sans-serif" font-size="24" font-weight="900" 
        text-anchor="middle" fill="url(#textGradient)" filter="url(#glow)">
    ISHTEEAHAR
  </text>
  
  <!-- Dot accent -->
  <circle cx="185" cy="45" r="3" fill="#ffd700" filter="url(#glow)"/>
  
  <!-- Slogan -->
  <text x="100" y="65" font-family="Arial, sans-serif" font-size="10" font-weight="600" 
        text-anchor="middle" fill="#ffd700" opacity="0.8">
    Digital Agency Excellence
  </text>
</svg>
