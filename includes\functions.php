<?php
/**
 * Common Functions for ISHTEEAHAR Digital Agency
 *
 * This file contains utility functions used throughout the application
 */

/**
 * Get all active services
 *
 * @return array Array of services
 */
function getServices() {
    try {
        if (function_exists('fetchAll')) {
            $sql = "SELECT * FROM services WHERE status = 'active' ORDER BY sort_order ASC, name ASC";
            return fetchAll($sql);
        }
    } catch (Exception $e) {
        // Return empty array if database not available
    }

    // Fallback services if database not available
    return [
        [
            'id' => 1,
            'name' => 'Web Development',
            'slug' => 'web-development',
            'description' => 'We create stunning, responsive websites that drive results. Our expert team specializes in modern web technologies, ensuring your online presence stands out in today\'s competitive digital landscape.',
            'short_description' => 'Custom websites and web applications built with cutting-edge technology',
            'icon' => 'icon-code',
            'status' => 'active'
        ],
        [
            'id' => 2,
            'name' => 'Digital Marketing',
            'slug' => 'digital-marketing',
            'description' => 'Boost your online visibility and reach your target audience with our comprehensive digital marketing strategies.',
            'short_description' => 'Strategic online marketing solutions to grow your business',
            'icon' => 'icon-trending_up',
            'status' => 'active'
        ],
        [
            'id' => 3,
            'name' => 'WordPress Development',
            'slug' => 'wordpress-development',
            'description' => 'Transform your ideas into powerful WordPress websites with custom themes and plugins.',
            'short_description' => 'Custom WordPress solutions for businesses of all sizes',
            'icon' => 'icon-wordpress',
            'status' => 'active'
        ]
    ];
}

/**
 * Get service by slug
 *
 * @param string $slug Service slug
 * @return array|false Service data or false if not found
 */
function getServiceBySlug($slug) {
    $sql = "SELECT * FROM services WHERE slug = ? AND status = 'active'";
    return fetchSingle($sql, [$slug]);
}

/**
 * Get portfolio items for a service
 *
 * @param int $service_id Service ID
 * @param int $limit Number of items to fetch
 * @param bool $featured_only Get only featured items
 * @return array Array of portfolio items
 */
function getPortfolioByService($service_id, $limit = null, $featured_only = false) {
    $sql = "SELECT p.*, s.name as service_name, s.slug as service_slug
            FROM portfolio_items p
            LEFT JOIN services s ON p.service_id = s.id
            WHERE p.service_id = ? AND p.status = 'active'";

    $params = [$service_id];

    if ($featured_only) {
        $sql .= " AND p.featured = 1";
    }

    $sql .= " ORDER BY p.sort_order ASC, p.created_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }

    return fetchAll($sql, $params);
}

/**
 * Get all portfolio items
 *
 * @param int $limit Number of items to fetch
 * @param bool $featured_only Get only featured items
 * @return array Array of portfolio items
 */
function getAllPortfolio($limit = null, $featured_only = false) {
    try {
        if (function_exists('fetchAll')) {
            $sql = "SELECT p.*, s.name as service_name, s.slug as service_slug
                    FROM portfolio_items p
                    LEFT JOIN services s ON p.service_id = s.id
                    WHERE p.status = 'active'";

            $params = [];

            if ($featured_only) {
                $sql .= " AND p.featured = 1";
            }

            $sql .= " ORDER BY p.sort_order ASC, p.created_at DESC";

            if ($limit) {
                $sql .= " LIMIT " . intval($limit);
            }

            return fetchAll($sql, $params);
        }
    } catch (Exception $e) {
        // Return empty array if database not available
    }

    // Return empty array as fallback
    return [];
}

/**
 * Get portfolio item by slug
 *
 * @param string $slug Portfolio item slug
 * @return array|false Portfolio item data or false if not found
 */
function getPortfolioBySlug($slug) {
    $sql = "SELECT p.*, s.name as service_name, s.slug as service_slug
            FROM portfolio_items p
            LEFT JOIN services s ON p.service_id = s.id
            WHERE p.slug = ? AND p.status = 'active'";
    return fetchSingle($sql, [$slug]);
}

/**
 * Save contact message
 *
 * @param array $data Contact form data
 * @return int Message ID
 */
function saveContactMessage($data) {
    $message_data = [
        'name' => sanitizeInput($data['name']),
        'email' => sanitizeInput($data['email']),
        'subject' => sanitizeInput($data['subject'] ?? 'Contact Form Submission'),
        'message' => sanitizeInput($data['message']),
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
    ];

    return insertData('contact_messages', $message_data);
}

/**
 * Send email notification
 *
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $from_name Sender name
 * @param string $from_email Sender email
 * @return bool True if sent successfully
 */
function sendEmailNotification($to, $subject, $message, $from_name = '', $from_email = '') {
    $headers = [];

    if ($from_name && $from_email) {
        $headers[] = "From: {$from_name} <{$from_email}>";
        $headers[] = "Reply-To: {$from_email}";
    } else {
        $headers[] = "From: " . getSetting('site_title', SITE_NAME) . " <" . getSetting('company_email', SITE_EMAIL) . ">";
    }

    $headers[] = "MIME-Version: 1.0";
    $headers[] = "Content-Type: text/html; charset=UTF-8";
    $headers[] = "X-Mailer: PHP/" . phpversion();

    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Generate breadcrumb navigation
 *
 * @param array $items Breadcrumb items
 * @return string HTML breadcrumb
 */
function generateBreadcrumb($items) {
    if (empty($items)) {
        return '';
    }

    $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';

    foreach ($items as $index => $item) {
        $is_last = ($index === count($items) - 1);

        if ($is_last) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($item['title']) . '</li>';
        } else {
            $html .= '<li class="breadcrumb-item"><a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['title']) . '</a></li>';
        }
    }

    $html .= '</ol></nav>';
    return $html;
}

/**
 * Generate meta tags for SEO
 *
 * @param array $meta Meta data
 * @return string HTML meta tags
 */
function generateMetaTags($meta = []) {
    $defaults = [
        'title' => getSetting('site_title', SITE_NAME),
        'description' => getSetting('site_tagline', SITE_TAGLINE),
        'keywords' => 'digital agency, web development, digital marketing, wordpress, graphic design, video editing, social media',
        'author' => getSetting('site_title', SITE_NAME),
        'image' => IMAGES_URL . '/logo.png',
        'url' => getCurrentURL()
    ];

    $meta = array_merge($defaults, $meta);

    $html = '';
    $html .= '<title>' . htmlspecialchars($meta['title']) . '</title>' . "\n";
    $html .= '<meta name="description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
    $html .= '<meta name="keywords" content="' . htmlspecialchars($meta['keywords']) . '">' . "\n";
    $html .= '<meta name="author" content="' . htmlspecialchars($meta['author']) . '">' . "\n";

    // Open Graph tags
    $html .= '<meta property="og:title" content="' . htmlspecialchars($meta['title']) . '">' . "\n";
    $html .= '<meta property="og:description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
    $html .= '<meta property="og:image" content="' . htmlspecialchars($meta['image']) . '">' . "\n";
    $html .= '<meta property="og:url" content="' . htmlspecialchars($meta['url']) . '">' . "\n";
    $html .= '<meta property="og:type" content="website">' . "\n";

    // Twitter Card tags
    $html .= '<meta name="twitter:card" content="summary_large_image">' . "\n";
    $html .= '<meta name="twitter:title" content="' . htmlspecialchars($meta['title']) . '">' . "\n";
    $html .= '<meta name="twitter:description" content="' . htmlspecialchars($meta['description']) . '">' . "\n";
    $html .= '<meta name="twitter:image" content="' . htmlspecialchars($meta['image']) . '">' . "\n";

    return $html;
}

/**
 * Get navigation menu items
 *
 * @return array Navigation items
 */
function getNavigationItems() {
    return [
        [
            'title' => 'Home',
            'url' => BASE_URL . '/index.php',
            'page' => 'index'
        ],
        [
            'title' => 'About',
            'url' => BASE_URL . '/about.php',
            'page' => 'about'
        ],
        [
            'title' => 'Services',
            'url' => BASE_URL . '/services.php',
            'page' => 'services'
        ],
        [
            'title' => 'Portfolio',
            'url' => BASE_URL . '/portfolio.php',
            'page' => 'portfolio'
        ],
        [
            'title' => 'Contact',
            'url' => BASE_URL . '/contact.php',
            'page' => 'contact'
        ]
    ];
}

/**
 * Format phone number for display
 *
 * @param string $phone Phone number
 * @return string Formatted phone number
 */
function formatPhone($phone) {
    $phone = preg_replace('/[^0-9]/', '', $phone);

    if (strlen($phone) === 10) {
        return sprintf('(%s) %s-%s',
            substr($phone, 0, 3),
            substr($phone, 3, 3),
            substr($phone, 6, 4)
        );
    }

    return $phone;
}

/**
 * Check if email is valid
 *
 * @param string $email Email address
 * @return bool True if valid
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate random string
 *
 * @param int $length String length
 * @return string Random string
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Log error message
 *
 * @param string $message Error message
 * @param string $file File name
 * @param int $line Line number
 */
function logError($message, $file = '', $line = 0) {
    $log_message = date('Y-m-d H:i:s') . " - Error: $message";
    if ($file) {
        $log_message .= " in $file";
    }
    if ($line) {
        $log_message .= " on line $line";
    }
    $log_message .= "\n";

    error_log($log_message, 3, ROOT_PATH . '/logs/error.log');
}
?>
