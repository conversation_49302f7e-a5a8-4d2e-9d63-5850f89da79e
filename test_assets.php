<?php
/**
 * Asset Loading Test
 * This file tests if CSS and JS files are loading correctly
 */

// Include configuration
require_once 'config/config.php';

echo "<h1>Asset Loading Test</h1>";

// Test CSS URLs
$css_files = [
    'vendor/bootstrap.min.css',
    'vendor/icomoon/style.css',
    'style.css'
];

echo "<h2>CSS Files Test:</h2>";
foreach ($css_files as $css_file) {
    $full_path = CSS_URL . '/' . $css_file;
    $local_path = 'css/' . $css_file;
    
    echo "<p><strong>$css_file:</strong><br>";
    echo "URL: <a href='$full_path' target='_blank'>$full_path</a><br>";
    
    if (file_exists($local_path)) {
        echo "<span style='color: green;'>✓ File exists locally</span>";
    } else {
        echo "<span style='color: red;'>✗ File NOT found locally</span>";
    }
    echo "</p>";
}

// Test JS URLs
$js_files = [
    'vendor/jquery-3.3.1.min.js',
    'vendor/bootstrap.min.js',
    'main.js'
];

echo "<h2>JavaScript Files Test:</h2>";
foreach ($js_files as $js_file) {
    $full_path = JS_URL . '/' . $js_file;
    $local_path = 'js/' . $js_file;
    
    echo "<p><strong>$js_file:</strong><br>";
    echo "URL: <a href='$full_path' target='_blank'>$full_path</a><br>";
    
    if (file_exists($local_path)) {
        echo "<span style='color: green;'>✓ File exists locally</span>";
    } else {
        echo "<span style='color: red;'>✗ File NOT found locally</span>";
    }
    echo "</p>";
}

// Test Images
$image_files = [
    'cover_bg_1.jpg',
    'cover_bg_2.jpg',
    'divider.png'
];

echo "<h2>Image Files Test:</h2>";
foreach ($image_files as $image_file) {
    $full_path = IMAGES_URL . '/' . $image_file;
    $local_path = 'images/' . $image_file;
    
    echo "<p><strong>$image_file:</strong><br>";
    echo "URL: <a href='$full_path' target='_blank'>$full_path</a><br>";
    
    if (file_exists($local_path)) {
        echo "<span style='color: green;'>✓ File exists locally</span>";
    } else {
        echo "<span style='color: red;'>✗ File NOT found locally</span>";
    }
    echo "</p>";
}

echo "<h2>Configuration Values:</h2>";
echo "<p><strong>SITE_URL:</strong> " . SITE_URL . "</p>";
echo "<p><strong>BASE_URL:</strong> " . BASE_URL . "</p>";
echo "<p><strong>CSS_URL:</strong> " . CSS_URL . "</p>";
echo "<p><strong>JS_URL:</strong> " . JS_URL . "</p>";
echo "<p><strong>IMAGES_URL:</strong> " . IMAGES_URL . "</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Homepage</a></p>";
?>
