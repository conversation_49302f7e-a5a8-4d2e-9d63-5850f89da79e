<?php
/**
 * ISHTEEAHAR Digital Agency - Portfolio Page
 *
 * Showcase of completed projects and work samples
 */

// Include configuration and functions
require_once 'config/config.php';

// Get filter parameters
$service_filter = $_GET['service'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$items_per_page = PORTFOLIO_PER_PAGE;

// Get services for filter
$services = getServices();

// Get portfolio items
if ($service_filter) {
    $service = getServiceBySlug($service_filter);
    if ($service) {
        $portfolio_items = getPortfolioByService($service['id']);
        $page_title = $service['name'] . ' Portfolio';
    } else {
        $portfolio_items = getAllPortfolio();
        $page_title = 'Portfolio';
        $service_filter = '';
    }
} else {
    $portfolio_items = getAllPortfolio();
    $page_title = 'Portfolio';
}

// Set page-specific meta data
$meta = [
    'title' => $page_title . ' - ' . getSetting('site_title', SITE_NAME),
    'description' => 'Explore our portfolio of successful digital projects including web development, digital marketing campaigns, WordPress sites, graphic design, and video editing work.',
    'keywords' => 'portfolio, projects, web development, digital marketing, wordpress, graphic design, video editing, case studies, ISHTEEAHAR'
];

// Include header
include 'includes/header.php';
?>

<style>
/* Animated Border Cards for Portfolio Page */
.portfolio-animated-card {
    position: relative;
    width: 100%;
    height: 350px;
    background: #1a2332;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 30px;
}

.portfolio-animated-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
}

.portfolio-animated-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -3s;
}

.portfolio-border-line {
    position: absolute;
    top: 0;
    inset: 0;
}

.portfolio-border-line::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -1.5s;
}

.portfolio-border-line::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -4.5s;
}

@keyframes rotateBorder {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.portfolio-card-content {
    position: relative;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.95));
    border-radius: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 340px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    margin: 4px;
}

.portfolio-image-section {
    height: 200px;
    background: linear-gradient(135deg, #2c5282, #1a365d);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.portfolio-content-section {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.portfolio-animated-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay-content {
    text-align: center;
    color: #000;
    font-weight: bold;
}

/* Service category cards */
.service-category-card {
    position: relative;
    width: 100%;
    height: 300px;
    background: #1a2332;
    border-radius: 15px;
    overflow: hidden;
}

.service-category-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 7s linear infinite;
}

.service-category-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 7s linear infinite;
    animation-delay: -3.5s;
}

.service-category-content {
    position: relative;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.95));
    padding: 30px 20px;
    border-radius: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 100%;
    min-height: 290px;
    margin: 3px;
}
</style>

            <!-- Page Header -->
            <div class="cover-v1 jarallax" style="height: 60vh; min-height: 400px;">
                <div class="container">
                    <div class="row align-items-center h-100">
                        <div class="col-md-8 mx-auto text-center">
                            <h1 class="heading hero-title text-3d"><i class="icon-briefcase heading-icon float"></i><?php echo htmlspecialchars($page_title); ?></h1>
                            <h2 class="subheading fade-in-up">Showcasing Our Best Work and Success Stories</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portfolio Filter -->
            <div class="unslate_co--section">

                                            </div>

            <!-- Portfolio Categories Overview -->
            <?php if (empty($service_filter)): ?>
            <div class="unslate_co--section">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal">Explore by Service</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row">
                        <?php foreach ($services as $index => $service): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="service-category-card fade-in-up" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                                <div class="service-category-content">
                                    <div class="text-center mb-3">
                                        <i class="<?php echo htmlspecialchars($service['icon']); ?>" style="font-size: 48px; color: #ffd700;"></i>
                                    </div>
                                    <h4 class="heading-3d text-center mb-3" style="color: #ffd700;"><?php echo htmlspecialchars($service['name']); ?></h4>
                                    <p class="text-center mb-4" style="color: rgba(255, 255, 255, 0.9);"><?php echo htmlspecialchars($service['short_description']); ?></p>

                                    <?php
                                    $service_portfolio_count = count(getPortfolioByService($service['id']));
                                    ?>
                                    <div class="text-center">
                                        <p class="small mb-3" style="color: rgba(255, 255, 255, 0.7);"><?php echo $service_portfolio_count; ?> Projects</p>
                                        <a href="portfolio.php?service=<?php echo urlencode($service['slug']); ?>"
                                           class="btn btn-custom-3d btn-sm">View Projects</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

            <?php endif; ?>

            <!-- Call to Action -->
            <div class="unslate_co--section">
                <div class="container">
                    <div class="row">
                        <div class="col-md-8 mx-auto text-center">
                            <div class="box-3d p-5">
                                <h2 class="heading-3d mb-4">Ready to Create Your Success Story?</h2>
                                <p class="lead mb-4">
                                    Let's discuss your project and create something amazing together.
                                    Our team is ready to bring your vision to life.
                                </p>
                                <a href="contact.php" class="btn btn-custom-3d btn-lg mr-3">Start Your Project</a>
                                <a href="services.php" class="btn btn-outline-pill btn-custom-light btn-lg">Our Services</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .portfolio-item:hover .project-details {
                    transform: translateY(0);
                }

                .portfolio-item:hover .portfolio-placeholder {
                    transform: scale(1.1);
                    transition: transform 0.3s ease;
                }

                .filter a {
                    transition: all 0.3s ease;
                }

                .portfolio-image {
                    border-radius: 10px;
                    overflow: hidden;
                }

                @media (max-width: 768px) {
                    .filter a {
                        margin-bottom: 10px;
                        display: inline-block;
                    }
                }
            </style>

            <script>
                $(document).ready(function() {
                    // Portfolio item hover effects
                    $('.portfolio-item').hover(
                        function() {
                            $(this).find('.portfolio-placeholder').css('transform', 'scale(1.1)');
                            $(this).find('.project-details').css('transform', 'translateY(0)');
                        },
                        function() {
                            $(this).find('.portfolio-placeholder').css('transform', 'scale(1)');
                            $(this).find('.project-details').css('transform', 'translateY(100%)');
                        }
                    );
                });
            </script>

<?php
// Include footer
include 'includes/footer.php';
?>
