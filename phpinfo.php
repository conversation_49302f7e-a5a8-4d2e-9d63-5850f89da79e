<?php
// Simple PHP info test
echo "<h1>PHP Configuration Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Server: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";

// Test if all required extensions are loaded
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli'];
echo "<h2>Required Extensions:</h2>";
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✓ $ext is loaded</p>";
    } else {
        echo "<p style='color: red;'>✗ $ext is NOT loaded</p>";
    }
}

echo "<hr>";
echo "<p><a href='test_db.php'>Test Database Connection</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
