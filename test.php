<?php
// Simple PHP test file to check if P<PERSON> is working
echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test if we can include files
if (file_exists('config/config.php')) {
    echo "<p>✅ Config file exists</p>";
    try {
        require_once 'config/config.php';
        echo "<p>✅ Config file loaded successfully</p>";
        
        // Test database connection
        if (function_exists('testDbConnection')) {
            if (testDbConnection()) {
                echo "<p>✅ Database connection successful</p>";
            } else {
                echo "<p>❌ Database connection failed</p>";
            }
        } else {
            echo "<p>⚠️ Database test function not available</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ Error loading config: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ Config file not found</p>";
}

echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
?>
