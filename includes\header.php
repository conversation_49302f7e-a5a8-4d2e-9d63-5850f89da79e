<?php
/**
 * Header Template for ISHTEEAHAR Digital Agency
 */

// Get current page for navigation
$current_page = basename($_SERVER['PHP_SELF'], '.php');
$navigation_items = getNavigationItems();
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <?php echo generateMetaTags($meta ?? []); ?>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo IMAGES_URL; ?>/favicon.ico">

    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/icomoon/style.css">
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/owl.carousel.min.css">
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/animate.min.css">
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/aos.css">
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/vendor/jquery.fancybox.min.css">

    <!-- Theme Style -->
    <link rel="stylesheet" href="<?php echo CSS_URL; ?>/style.css">

    <!-- Custom Styles for ISHTEEAHAR -->
    <style>
        /* Enhanced Modern 3D Theme - Matching Logo Style */
        body, .unslate_co--site-inner {
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
                linear-gradient(135deg,rgb(255, 255, 255) 0%,rgb(100, 100, 100) 25%, #1a365d 50%, #2d3748 75%, #1a202c 100%);
            color: #ffffff !important;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Background Particles */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(2px 2px at 20px 30px, rgba(255, 215, 0, 0.3), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255, 215, 0, 0.2), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(255, 215, 0, 0.4), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255, 215, 0, 0.2), transparent);
            background-repeat: repeat;
            background-size: 150px 100px;
            animation: particleFloat 20s linear infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes particleFloat {
            0% { transform: translateY(0px) rotate(0deg); }
            100% { transform: translateY(-100px) rotate(360deg); }
        }

        /* Ensure all text is white */
        body, body *, p, span, div, h1, h2, h3, h4, h5, h6,
        .unslate_co--site-inner, .unslate_co--site-inner * {
            color: #ffffff !important;
        }

        /* Exception for links and special elements */
        a, .btn, .logo-slogan {
            color: inherit !important;
        }

        /* Specific white text overrides */
        .feature-v1 h3, .feature-v1 p,
        .portfolio-item-content h3, .portfolio-item-content p,
        .section-heading-wrap h2, .section-heading-wrap span,
        .hero-title, .subheading,
        .unslate_co--section p, .unslate_co--section h1,
        .unslate_co--section h2, .unslate_co--section h3 {
            color: #ffffff !important;
        }

        /* Navigation text */
        .site-nav-ul li a, .unslate_co--site-nav a {
            color: #ffffff !important;
        }

        /* Override any dark text from main CSS */
        .text-dark, .text-muted, .text-secondary {
            color:rgb(0, 0, 0) !important;
        }

        /* Ensure paragraphs and content are white */
        .container p, .row p, .col p,
        .feature-v1 *, .portfolio-item *,
        .unslate_co--section * {
            color: #ffffff !important;
        }

        /* 3D Text Effects */
        .text-3d {
            text-shadow:
                1px 1px 0px #1a2332,
                2px 2px 0px #1a2332,
                3px 3px 0px #1a2332,
                4px 4px 0px #1a2332,
                5px 5px 10px rgba(26, 35, 50, 0.8);
        }

        .heading-3d {
            background: linear-gradient(45deg, #ffd700, #2c5282, #ffed4e, #1a365d, #ffd700);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: headingGradientShift 5s ease-in-out infinite;
            font-weight: 700;
            position: relative;
            filter: none !important;
            backdrop-filter: none !important;
            text-shadow: none !important;
            -webkit-filter: none !important;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        @keyframes headingGradientShift {
            0% { background-position: 0% 50%; }
            25% { background-position: 50% 0%; }
            50% { background-position: 100% 50%; }
            75% { background-position: 50% 100%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes headingGlow {
            0% {
                filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
                transform: scale(1);
            }
            100% {
                filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
                transform: scale(1.02);
            }
        }

        /* Advanced 3D Box Effects - Modern Dynamic Style */
        .box-3d {
            background:
                linear-gradient(145deg, rgba(44, 82, 130, 0.9), rgba(26, 54, 93, 0.9)),
                linear-gradient(45deg, rgba(255, 215, 0, 0.1), transparent);
            backdrop-filter: blur(10px);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 15px 35px rgba(30, 58, 95, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2),
                0 0 0 1px rgba(255, 215, 0, 0.3);
            border: 2px solid;
            border-image: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700) 1;
            border-radius: 20px;
            position: relative;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .box-3d::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700, #ffed4e);
            border-radius: 22px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite alternate;
        }

        @keyframes borderGlow {
            0% { opacity: 0.5; transform: scale(1); }
            100% { opacity: 0.8; transform: scale(1.02); }
        }

        .box-3d:hover {
            transform: translateY(-15px) rotateX(10deg) rotateY(5deg) scale(1.02);
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.4),
                0 25px 50px rgba(30, 58, 95, 0.5),
                inset 0 2px 0 rgba(255, 255, 255, 0.2),
                inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                0 0 30px rgba(255, 215, 0, 0.4);
        }

        /* Enhanced Animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in-up.animated {
            opacity: 1;
            transform: translateY(0);
        }

        .zoom-in {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.6s ease;
        }

        .zoom-in.animated {
            opacity: 1;
            transform: scale(1);
        }

        /* Advanced Service Cards - 3D Dynamic Style */
        .feature-v1 {
            background:
                linear-gradient(145deg, rgba(44, 82, 130, 0.95), rgba(26, 54, 93, 0.95)),
                radial-gradient(circle at 30% 30%, rgba(255, 215, 0, 0.1), transparent 70%);
            border-radius: 25px;
            padding: 40px 30px;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 10px 25px rgba(30, 58, 95, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
            border: 2px solid transparent;
            background-clip: padding-box;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }

        .feature-v1::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #ffd700, transparent, #ffd700);
            border-radius: 25px;
            padding: 2px;
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            animation: borderRotate 4s linear infinite;
        }

        @keyframes borderRotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-v1::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
            transform: rotate(-45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .feature-v1:hover {
            transform: translateY(-20px) rotateX(15deg) rotateY(10deg) scale(1.05);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.4),
                0 20px 40px rgba(30, 58, 95, 0.5),
                inset 0 2px 0 rgba(255, 255, 255, 0.2),
                0 0 40px rgba(255, 215, 0, 0.3);
        }

        .feature-v1:hover::after {
            opacity: 1;
            transform: rotate(-45deg) translate(50%, 50%);
        }

        .feature-v1 .wrap-icon {
            position: relative;
            z-index: 2;
            margin-bottom: 25px;
        }

        .feature-v1 .wrap-icon i {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
            transition: all 0.3s ease;
        }

        .feature-v1:hover .wrap-icon i {
            transform: scale(1.2) rotateY(360deg);
            filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
        }

        /* Advanced Portfolio Items - 3D Dynamic Style */
        .portfolio-item {
            border-radius: 25px;
            overflow: hidden;
            position: relative;
            background:
                linear-gradient(145deg, rgba(44, 82, 130, 0.9), rgba(26, 54, 93, 0.9));
            backdrop-filter: blur(10px);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 10px 25px rgba(30, 58, 95, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            border: 2px solid transparent;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
        }

        .portfolio-item::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
            border-radius: 27px;
            z-index: -1;
            animation: portfolioBorderPulse 2s ease-in-out infinite alternate;
        }

        @keyframes portfolioBorderPulse {
            0% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .portfolio-item:hover {
            transform: translateY(-15px) rotateX(10deg) rotateY(5deg) scale(1.05);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.4),
                0 20px 40px rgba(30, 58, 95, 0.5),
                0 0 30px rgba(255, 215, 0, 0.3);
        }

        .portfolio-item .overlay {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
            backdrop-filter: blur(5px);
            transition: all 0.4s ease;
        }

        /* Button Enhancements */
        .btn-custom-3d {
            background: linear-gradient(145deg, #1a2332, #0f1419);
            border: 2px solid #ffd700;
            color: #fff;
            box-shadow:
                5px 5px 15px rgba(15, 20, 25, 0.8),
                -5px -5px 15px rgba(26, 35, 50, 0.4);
            transition: all 0.3s ease;
        }

        .btn-custom-3d:hover {
            background: linear-gradient(145deg, #26354a, #1a2332);
            transform: translateY(-2px);
            box-shadow:
                7px 7px 20px rgba(15, 20, 25, 0.9),
                -7px -7px 20px rgba(26, 35, 50, 0.5);
            border-color: #ffed4e;
            color: #fff;
        }

        /* Logo Enhancement */
        .unslate_co--site-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-image {
            height:80px;
            width: auto;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
            transition: all 0.3s ease;
        }

        .logo-image:hover {
            filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.6));
            transform: scale(1.05);
        }

        /* Navigation Enhancement */
        .site-nav-ul > li > a {
            position: relative;
            transition: all 0.3s ease;
        }

        .site-nav-ul > li > a:hover {
            text-shadow: 0 0 10px rgba(255,255,255,0.5);
        }

        /* Hero Section Enhancement */
        .cover-v1 {
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.9), rgba(26, 35, 50, 0.7)),
                        url('<?php echo IMAGES_URL; ?>/cover_bg_1.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            border-bottom: 3px solid #ffd700;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: 900;
            background: linear-gradient(45deg, #fff,rgb(255, 255, 255), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgb(255, 238, 0);
            animation: glow 2s ease-in-out infinite alternate;
        }

        /* Advanced Hero Section - 3D Dynamic Style */
        .cover-v1 {
            background:

                linear-gradient(135deg,rgba(0, 0, 0, 0.92), rgba(43, 43, 43, 0.95)),
                url('<?php echo IMAGES_URL; ?>/tt.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
            overflow: hidden;
            border-bottom: 5px solid;
            /* border-image: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700) 1; */
        }

        .cover-v1::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 30%),
                radial-gradient(circle at 80% 50%, rgba(255, 215, 0, 0.1) 0%, transparent 30%);
            animation: heroParticles 15s ease-in-out infinite alternate;
        }

        @keyframes heroParticles {
            0% { opacity: 0.3; transform: scale(1) rotate(0deg); }
            100% { opacity: 0.7; transform: scale(1.1) rotate(5deg); }
        }

        .hero-title {
            font-size: 4.5rem;
            font-weight: 900;

            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
           position: relative;
            z-index: 2;
            transform-style: preserve-3d;
        }

        @keyframes heroGlow {
            from {
                filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
                transform: perspective(1000px) rotateX(0deg) rotateY(0deg);
            }
            to {
                filter: drop-shadow(0 0 30px rgba(255, 215, 0, 0.8));
                transform: perspective(1000px) rotateX(2deg) rotateY(1deg);
            }
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .subheading {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            animation: subtitleFloat 4s ease-in-out infinite alternate;
        }

        @keyframes subtitleFloat {
            0% { transform: translateY(0px); opacity: 0.8; }
            100% { transform: translateY(-5px); opacity: 1; }
        }

        /* Stylish Heading Icons */
        .heading-icon {
            display: inline-block;
            margin-right: 15px;
            font-size: 1.2em;
            background: linear-gradient(45deg, #ffd700, #2c5282, #ffed4e, #1a365d);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: iconPulse 3s ease-in-out infinite, iconGradientShift 4s ease-in-out infinite;
            filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
            transform-origin: center;
        }

        @keyframes iconGradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes iconPulse {
            0%, 100% {
                transform: scale(1) rotate(0deg);
                filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
            }
            50% {
                transform: scale(1.1) rotate(5deg);
                filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.6));
            }
        }

        .heading-icon.rotate {
            animation: iconRotate 4s linear infinite;
        }

        @keyframes iconRotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        .heading-icon.bounce {
            animation: iconBounce 2s ease-in-out infinite;
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-5px) scale(1.05); }
        }

        .heading-icon.glow {
            animation: iconGlow 3s ease-in-out infinite alternate;
        }

        @keyframes iconGlow {
            0% {
                filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
                transform: scale(1);
            }
            100% {
                filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
                transform: scale(1.1);
            }
        }

        .heading-icon.float {
            animation: iconFloat 4s ease-in-out infinite;
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-3px) rotate(2deg); }
            50% { transform: translateY(-6px) rotate(0deg); }
            75% { transform: translateY(-3px) rotate(-2deg); }
        }

        /* Section heading enhancements */
        .heading-h2 {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            background: linear-gradient(45deg, #ffd700, #2c5282, #ffed4e, #1a365d, #ffd700);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: headingGradientShift 5s ease-in-out infinite;
            font-weight: 900;
            filter: none !important;
            backdrop-filter: none !important;
            text-shadow: none !important;
            -webkit-filter: none !important;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .heading-h2 .gsap-reveal {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .heading-h3 {
            background: linear-gradient(45deg, #ffd700, #2c5282, #ffed4e, #1a365d);
            background-size: 250% 250%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: headingGradientShift 6s ease-in-out infinite;
            font-weight: 700;
            text-shadow:
                0 0 10px rgba(255, 215, 0, 0.3),
                0 0 20px rgba(44, 82, 130, 0.2);
        }

        /* Hero title icon */
        .hero-title .heading-icon {
            font-size: 0.8em;
            margin-right: 20px;
            animation: iconFloat 4s ease-in-out infinite;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .box-3d {
                box-shadow: 10px 10px 30px rgba(0, 0, 0, 0.3), -10px -10px 30px rgba(30, 58, 95, 0.4);
            }

            .feature-v1 {
                padding: 30px 20px;
            }

            .heading-icon {
                font-size: 1em;
                margin-right: 10px;
            }

            .hero-title .heading-icon {
                font-size: 0.7em;
                margin-right: 15px;
            }
        }

        /* 3D Animated Stylish Navbar Styles */
        .navbar-3d-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.95), rgba(26, 35, 50, 0.95), rgba(44, 82, 130, 0.9));
            backdrop-filter: blur(20px);
            border-bottom: 3px solid;
            border-image: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700) 1;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .navbar-3d-wrapper {
            position: relative;
            padding: 15px 0;
            overflow: hidden;
        }

        /* Navbar Background Effects */
        .navbar-bg-effects {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .navbar-particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #ffd700;
            border-radius: 50%;
            animation: navParticleFloat 6s ease-in-out infinite;
            box-shadow: 0 0 6px rgba(255, 215, 0, 0.4);
        }

        @keyframes navParticleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-10px) rotate(180deg); opacity: 1; }
        }

        .navbar-particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .navbar-particle:nth-child(2) { top: 60%; left: 30%; animation-delay: 1s; }
        .navbar-particle:nth-child(3) { top: 40%; right: 25%; animation-delay: 2s; }
        .navbar-particle:nth-child(4) { top: 80%; right: 15%; animation-delay: 3s; }
        .navbar-particle:nth-child(5) { top: 30%; left: 70%; animation-delay: 4s; }

        /* Navbar Geometric Shapes */
        .navbar-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .navbar-shape {
            position: absolute;
            border: 1px solid rgba(255, 215, 0, 0.1);
            animation: navShapeRotate 8s linear infinite;
        }

        .navbar-shape.circle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            top: 30%;
            right: 10%;
        }

        .navbar-shape.square {
            width: 18px;
            height: 18px;
            top: 50%;
            left: 8%;
            animation-delay: 2s;
        }

        .navbar-shape.triangle {
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 17px solid rgba(255, 215, 0, 0.1);
            border-radius: 0;
            top: 70%;
            right: 20%;
            animation-delay: 4s;
        }

        @keyframes navShapeRotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* Navbar Content */
        .navbar-3d-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 10;
        }

        /* Navigation Links */
        .nav-links-3d {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 30px;
        }

        .nav-item-3d {
            position: relative;
        }

        .nav-link-3d {
            position: relative;
            display: block;
            padding: 12px 20px;
            color: #ffffff;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s ease;
            border-radius: 25px;
            background: rgba(26, 35, 50, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            overflow: hidden;
        }

        .nav-text {
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .nav-hover-effect {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.2));
            transition: all 0.4s ease;
            z-index: 1;
        }

        .nav-link-3d:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
            border-color: rgba(255, 215, 0, 0.5);
            color: #ffd700;
        }

        .nav-link-3d:hover .nav-hover-effect {
            left: 0;
        }

        .nav-link-3d:hover .nav-text {
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        /* Active Link */
        .nav-link-3d.active {
            background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 237, 78, 0.2));
            border-color: #ffd700;
            color: #ffd700;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
        }

        /* 3D Logo Styles */
        .navbar-logo-3d {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            z-index: 20;
        }

        .logo-link-3d {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            transition: all 0.4s ease;
        }

        .logo-container-3d {
            position: relative;
            margin-bottom: 5px;
        }

        .logo-img-3d {
            height: 60px;
            width: auto;
            transition: all 0.4s ease;
            filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.4));
        }

        .logo-glow {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.3), transparent 70%);
            border-radius: 50%;
            animation: logoGlow 3s ease-in-out infinite alternate;
            z-index: -1;
        }

        @keyframes logoGlow {
            0% { opacity: 0.3; transform: scale(1); }
            100% { opacity: 0.7; transform: scale(1.1); }
        }

        .logo-text-3d {
            font-size: 0.9rem;
            font-weight: 700;
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
            letter-spacing: 2px;
            transition: all 0.3s ease;
        }

        .logo-link-3d:hover .logo-img-3d {
            transform: scale(1.1) rotateY(10deg);
            filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8));
        }

        .logo-link-3d:hover .logo-text-3d {
            color: #ffed4e;
            text-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
            transform: scale(1.05);
        }

        /* Mobile Menu */
        .mobile-menu-3d {
            display: none;
        }

        .mobile-toggle-3d {
            background: rgba(26, 35, 50, 0.3);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 15px;
            padding: 10px 15px;
            color: #ffffff;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 3px;
        }

        .hamburger-line {
            width: 20px;
            height: 2px;
            background: #ffd700;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .menu-text {
            font-size: 0.8rem;
            margin-top: 5px;
            color: #ffd700;
        }

        .mobile-toggle-3d:hover {
            background: rgba(255, 215, 0, 0.1);
            border-color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
        }

        /* Responsive Design */
        @media (max-width: 991px) {
            .navbar-left, .navbar-right {
                display: none;
            }

            .mobile-menu-3d {
                display: block;
                position: absolute;
                right: 20px;
                top: 50%;
                transform: translateY(-50%);
            }

            .navbar-logo-3d {
                position: relative;
                left: auto;
                top: auto;
                transform: none;
            }

            .navbar-3d-content {
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            .navbar-3d-wrapper {
                padding: 10px 0;
            }

            .logo-img-3d {
                height: 50px;
            }

            .logo-text-3d {
                font-size: 0.8rem;
            }
        }
    </style>

    <?php if (isset($additional_css)): ?>
        <?php echo $additional_css; ?>
    <?php endif; ?>
</head>
<body data-spy="scroll" data-target=".site-nav-target" data-offset="200">

    <!-- Mobile Menu -->
    <nav class="unslate_co--site-mobile-menu">
        <div class="close-wrap d-flex">
            <a href="#" class="d-flex ml-auto js-menu-toggle">
                <span class="close-label">Close</span>
                <div class="close-times">
                    <span class="bar1"></span>
                    <span class="bar2"></span>
                </div>
            </a>
        </div>
        <div class="site-mobile-inner"></div>
    </nav>

    <div class="unslate_co--site-wrap">
        <div class="unslate_co--site-inner">

            <!-- Animated Lines Background -->
            <div class="lines-wrap">
                <div class="lines-inner">
                    <div class="lines"></div>
                </div>
            </div>

            <!-- 3D Animated Stylish Navigation -->
            <nav class="navbar-3d-container">
                <div class="navbar-3d-wrapper">
                    <!-- Animated Background Effects -->
                    <div class="navbar-bg-effects">
                        <div class="navbar-particle"></div>
                        <div class="navbar-particle"></div>
                        <div class="navbar-particle"></div>
                        <div class="navbar-particle"></div>
                        <div class="navbar-particle"></div>
                    </div>

                    <!-- Geometric Shapes -->
                    <div class="navbar-shapes">
                        <div class="navbar-shape circle"></div>
                        <div class="navbar-shape square"></div>
                        <div class="navbar-shape triangle"></div>
                    </div>

                    <div class="container">
                        <div class="navbar-3d-content">
                            <!-- Left Navigation -->
                            <div class="navbar-left">
                                <ul class="nav-links-3d">
                                    <?php foreach (array_slice($navigation_items, 0, 2) as $item): ?>
                                    <li class="nav-item-3d">
                                        <a href="<?php echo $item['url']; ?>"
                                           class="nav-link-3d <?php echo isActivePage($item['page']); ?>">
                                            <span class="nav-text"><?php echo $item['title']; ?></span>
                                            <span class="nav-hover-effect"></span>
                                        </a>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>

                            <!-- Center Logo -->
                            <div class="navbar-logo-3d">
                                <a href="<?php echo BASE_URL; ?>/index.php" class="logo-link-3d">
                                    <div class="logo-container-3d">
                                        <img src="<?php echo IMAGES_URL; ?>/logo.jpeg"
                                             alt="<?php echo getSetting('site_title', SITE_NAME); ?>"
                                             class="logo-img-3d">
                                        <div class="logo-glow"></div>
                                    </div>
                            
                                </a>
                            </div>

                            <!-- Right Navigation -->
                            <div class="navbar-right">
                                <ul class="nav-links-3d">
                                    <?php foreach (array_slice($navigation_items, 2) as $item): ?>
                                    <li class="nav-item-3d">
                                        <a href="<?php echo $item['url']; ?>"
                                           class="nav-link-3d <?php echo isActivePage($item['page']); ?>">
                                            <span class="nav-text"><?php echo $item['title']; ?></span>
                                            <span class="nav-hover-effect"></span>
                                        </a>
                                    </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>

                            <!-- Mobile Menu Toggle -->
                            <div class="mobile-menu-3d">
                                <button class="mobile-toggle-3d js-menu-toggle">
                                    <span class="hamburger-line"></span>
                                    <span class="hamburger-line"></span>
                                    <span class="hamburger-line"></span>
                                    <span class="menu-text">Menu</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            <!-- END 3D Navigation -->
