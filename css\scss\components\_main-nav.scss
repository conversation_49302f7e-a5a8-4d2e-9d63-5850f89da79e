.unslate_co--site-nav {
	padding-top: 30px;
	padding-bottom: 30px;
	position: absolute;
	left: 0;
	font-weight: 400;
	z-index: 1002;
	width: 100%;
	top: 0;
	.container {
		position: relative;
	}
	.site-logo {
		
		z-index: 99;
		&.pos-absolute {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}
	}
	.site-nav-ul-wrap {
		width: 100%;
		margin: 0 auto;
		z-index: 1;
		position: relative;
	}
	.unslate_co--site-logo {
		font-size: 1.7rem;
		color: $white;
		font-weight: 700;
		span {
			color: $primary;
		}
	}
	.site-nav-ul, .site-nav-ul-none-onepage {
		text-align: center;
		&, & > li {
			padding: 0;
			margin: 0;
			list-style: none;
		}
		> li {
			text-align: left;
			display: inline-block;
			> a {
				padding: 10px 7px;
				display: block;
				color: $white;
				font-size: 14px;
				position: relative;
				&:before {
					left: 7px;
					right: 7px;
					content: "";
					position: absolute;
					height: 1px;
					background: $white;
					bottom: 0;
					width: 0%;
					transition: .2s width ease-in-out;
				}
				&:hover {
					color: $white;
					&:before {
						width: calc(100% - 14px);
					}
				}
				&.active {
					position: relative;
					&:before {
						color: $white;
						width: calc(100% - 14px);
					}
				}
			}
			&.has-children {
				position: relative;
				
				> .dropdown, > ul {
					visibility: hidden;
					opacity: 0;
					z-index: 2;
					position: absolute;
					left: 0;
					top: 100%;
					min-width: 200px;
					background: $white;
					padding: 20px 0px;
					margin-top: 20px;
					box-shadow: 0 15px 30px 0 rgba($black, .2);

					// transition: .5s all cubic-bezier(.23,1,.32,1);
					transition: 0.2s 0s;
					> li {
						padding: 0;
						margin: 0;
						list-style: none;
						line-height: 1.5;
					}
					> li {
						display: block;
						a {
							font-size: 14px;
							padding-top: 5px;
							padding-bottom: 5px;
							padding-left: 30px;
							padding-right: 30px;
							display: block;
							color: lighten($black, 50%);
							&:hover {
								color: $black;
								background: lighten(#efefef, 4%);
								padding-left: 30px;
							}
						}
						&.has-children {
							position: relative;
							> a {
								position: relative;
								&:before {
									position: absolute;
									content: "\e315";
									font-size: 16px;
									top: 50%;
									color: $black;
									right: 20px;
									transform: translateY(-50%);
									font-family: 'icomoon';
								}
							}
							> .dropdown, > ul {
								z-index: 1;
								visibility: hidden;
								opacity: 0;
								position: absolute;
								left: 0;
								z-index: 22;
								list-style: none;
								top: 0;
								left: 100%;
								min-width: 200px;
								background: $white;
								padding: 20px 0px;
								margin-top: 20px;
								box-shadow: 0 15px 30px 0 rgba($black, .2);
								// box-shadow: none;
								transition: 0.2s 0s;
								a {
									padding-left: 30px;
									padding-right: 30px;
								}
							}

						}
						&:hover, &:focus, &:active {
							cursor: pointer;
							> .dropdown {
								transition-delay: 0s;
								margin-top: 0px;
								visibility: visible;
								opacity: 1;
								// left: 100%;
								// top: 0;
							}
						}
						/* 2nd level hover*/
					}
				}

				&:hover, &:focus, &:active {
					cursor: pointer;
					> .dropdown {
						transition-delay: 0s;
						margin-top: 0px;
						visibility: visible;
						opacity: 1;
					}
				}

			}
			&.active {
				> a {
					position: relative;
					&:before {
						color: $white;
						width: calc(100% - 40px);
					}
				}
			}
		}
	}	

	

	// burger toggle menu
	.burger-toggle-menu {
		display: block;
	  width: 36px;
  	// height: 45px;
  	// margin-top: 50px;
  	// margin-bottom: 50px;
	  position: relative;
	  -webkit-transform: rotate(0deg);
	  -moz-transform: rotate(0deg);
	  -o-transform: rotate(0deg);
	  transform: rotate(0deg);
	  -webkit-transition: .5s ease-in-out;
	  -moz-transition: .5s ease-in-out;
	  -o-transition: .5s ease-in-out;
	  transition: .5s ease-in-out;
	  cursor: pointer;
	  z-index: 99;
	  span {
		  display: block;
		  position: absolute;
		  height: 2px;
		  width: 100%;
		  background: $white;
		  border-radius: 0px;
		  opacity: 1;
		  left: 0;
		  -webkit-transform: rotate(0deg);
		  -moz-transform: rotate(0deg);
		  -o-transform: rotate(0deg);
		  transform: rotate(0deg);
		  -webkit-transition: .25s ease-in-out;
		  -moz-transition: .25s ease-in-out;
		  -o-transition: .25s ease-in-out;
		  transition: .25s ease-in-out;
		  &:nth-child(1) {
			  top: 0px;
			  -webkit-transform-origin: left center;
			  -moz-transform-origin: left center;
			  -o-transform-origin: left center;
			  transform-origin: left center;
			}
			&:nth-child(2) {
			  top: 10px;
			  -webkit-transform-origin: left center;
			  -moz-transform-origin: left center;
			  -o-transform-origin: left center;
			  transform-origin: left center;
			}
			&:nth-child(3) {
			  top: 20px;
			  -webkit-transform-origin: left center;
			  -moz-transform-origin: left center;
			  -o-transform-origin: left center;
			  transform-origin: left center;
			}
		}
		&.open {
			span:nth-child(1) {
			  -webkit-transform: rotate(45deg);
			  -moz-transform: rotate(45deg);
			  -o-transform: rotate(45deg);
			  transform: rotate(45deg);
			  top: -3px;
			  // left: 8px;
			  left: 0;
			}

			span:nth-child(2) {
			  width: 0%;
			  opacity: 0;
			}
			
			span:nth-child(3) {
			  -webkit-transform: rotate(-45deg);
			  -moz-transform: rotate(-45deg);
			  -o-transform: rotate(-45deg);
			  transform: rotate(-45deg);
			  // top: 39px;
			  top: 23px;
			  // left: 8px;
			  left: 0;
			}

		}
	}


	&.scrolled  {
		position: fixed;
		right: 0;
		left: 0;
		top: 0;
		margin-top: -130px;
		background: $white!important;
		box-shadow: 0 0 10px 0 rgba(0,0,0,.1);
		padding-top: 5px;
		padding-bottom: 5px;

	
		.burger-toggle-menu {
			top: 10px;
			span {
				background: $black;
			}
		}
		.unslate_co--site-logo {
			color: $black;
		}
		.site-nav-ul, .site-nav-ul-none-onepage {
			text-align: center;
			&, & > li {
				padding: 0;
				margin: 0;
				list-style: none;
			}
			> li {
				text-align: left;
				display: inline-block;
				> a {
					
					display: block;
					color: $black;
					&:hover {
						color: $primary;
					}
					&:before {
						background: $white;
					}
					&.active {
						color: $primary;
						&:before {
							background: $white;
						}	
					}
				}
			}
		}
		
		&.awake {
			margin-top: 0px;
			transition: .3s all ease-out;
		}
		&.sleep {
			transition: .3s all ease-out;	
		}

	}
}



/* Mobile nav */
.unslate_co--site-mobile-menu {
	// display: none;

	position: fixed;
	z-index: 1009;
	height: 100vh;
	background: $white;
	width: 280px;
	overflow: auto;
	padding-top: 30px;
	padding-bottom: 70px;
	width: 280px;
	left: 0;
	transform: translateX(-100%);
	transition: 1s transform cubic-bezier(.23,1,.32,1);

	.offcanvas & {
		transform: translateX(0%);
	}

	.close-wrap {
		margin-right: 20px;
		position: relative;
		a {
			margin-right: 15px;
			&:hover {
				opacity: .5;
			}
			.close-label {
				margin-right: 20px;
				color: $black;
			}
			.close-times {
				position: relative;
				margin-top: 4px;
				right: 0;
				.bar1, .bar2 {
					width: 2px;
					height: 20px;
					background: $black;
					position: absolute;
				} 
				.bar1 {
					transform: rotate(45deg);
				}
				.bar2 {
					transform: rotate(-45deg);
				}
			}	
		}
	}
	ul {
		&, & li {
			padding: 0;
			margin: 0;
			position: relative;
			a {
				position: relative;
				display: block;
				padding-top: 10px;
				padding-bottom: 10px;
				padding-left: 20px;
				padding-right: 20px;
				color: $black;
				&:hover {
					color: $primary;
				}
			}
		}
		> li {
			> ul {
				> li {
					> a {
						position: relative;
						padding-left: 30px;
						font-size: 14px;
						padding-top: 5px;
						padding-bottom: 5px;
					}
					> ul {
						> li {
							> a {
								position: relative;
								padding-left: 50px;
								font-size: 14px;
								padding-top: 5px;
								padding-bottom: 5px;	
							}
						}
					}
				}
			}
			&.has-children {
				.arrow-collapse {
					position: absolute;
					right: 10px;
					top: 0px;
					z-index: 20;
					width: 45px;
					height: 45px;
					text-align: center;
					cursor: pointer;
					border-radius: 50%;
					&:hover {
						// background: $gray-100;
					}
					&:before {
						font-size: 11px!important;
						z-index: 20;
						font-family: "icomoon";
						content: "\f078";
						position: absolute;
						top: 50%;
						left: 50%;
						color: $black;
						transform: translate(-50%, -50%) rotate(-180deg);
						transition: .3s all ease;
					}
					&.collapsed {
						&:before {
							transform: translate(-50%, -50%);	
						}
					}
				}

				> ul {
					> li {
						&.has-children {
							.arrow-collapse {
								
								top: -10px;
								
							}
						}
					}
				}
			}
		}
	}
}


