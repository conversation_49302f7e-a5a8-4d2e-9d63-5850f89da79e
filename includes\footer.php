        </div> <!-- END .unslate_co-site-inner -->

        <!-- Footer -->
        <footer class="unslate_co--footer unslate_co--section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-7">

                        <!-- Footer Logo -->
                        <div class="footer-site-logo">
                            <a href="<?php echo BASE_URL; ?>/index.php">
                                <img src="<?php echo IMAGES_URL; ?>/logo.svg" alt="<?php echo getSetting('site_title', SITE_NAME); ?>" style="height: 60px; filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));">
                            </a>
                        </div>

                        <!-- Social Media Links -->
                        <ul class="footer-site-social">
                            <?php if (getSetting('facebook_url') && getSetting('facebook_url') !== '#'): ?>
                            <li><a href="<?php echo getSetting('facebook_url'); ?>" target="_blank" rel="noopener">Facebook</a></li>
                            <?php endif; ?>

                            <?php if (getSetting('twitter_url') && getSetting('twitter_url') !== '#'): ?>
                            <li><a href="<?php echo getSetting('twitter_url'); ?>" target="_blank" rel="noopener">Twitter</a></li>
                            <?php endif; ?>

                            <?php if (getSetting('instagram_url') && getSetting('instagram_url') !== '#'): ?>
                            <li><a href="<?php echo getSetting('instagram_url'); ?>" target="_blank" rel="noopener">Instagram</a></li>
                            <?php endif; ?>

                            <?php if (getSetting('linkedin_url') && getSetting('linkedin_url') !== '#'): ?>
                            <li><a href="<?php echo getSetting('linkedin_url'); ?>" target="_blank" rel="noopener">LinkedIn</a></li>
                            <?php endif; ?>

                            <?php if (getSetting('youtube_url') && getSetting('youtube_url') !== '#'): ?>
                            <li><a href="<?php echo getSetting('youtube_url'); ?>" target="_blank" rel="noopener">YouTube</a></li>
                            <?php endif; ?>
                        </ul>

                        <!-- Copyright -->
                        <p class="site-copyright">
                            Copyright &copy; <script>document.write(new Date().getFullYear());</script>
                            <?php echo getSetting('company_name', SITE_NAME); ?>. All rights reserved.
                            <br>
                            <small>Powered by Excellence in Digital Solutions</small>
                        </p>

                    </div>
                </div>
            </div>
        </footer>

    </div> <!-- END .unslate_co--site-wrap -->

    <!-- Loader -->
    <div id="unslate_co--overlayer"></div>
    <div class="site-loader-wrap">
        <div class="site-loader"></div>
    </div>

    <!-- JavaScript Files -->
    <script src="<?php echo JS_URL; ?>/vendor/jquery-3.3.1.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/popper.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/bootstrap.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/owl.carousel.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.waypoints.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.easing.1.3.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.animateNumber.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.fancybox.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.validate.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/aos.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/imagesloaded.pkgd.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/isotope.pkgd.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jarallax.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jarallax-video.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/TweenMax.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/ScrollMagic.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/scrollmagic.animation.gsap.min.js"></script>

    <!-- Main JavaScript -->
    <script src="<?php echo JS_URL; ?>/scripts-dist.js"></script>
    <script src="<?php echo JS_URL; ?>/main.js"></script>

    <!-- Custom JavaScript for ISHTEEAHAR -->
    <script>
        $(document).ready(function() {
            // Initialize AOS (Animate On Scroll)
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                mirror: false
            });

            // Custom scroll animations
            $(window).scroll(function() {
                var scrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();

                $('.fade-in-up').each(function() {
                    var elementTop = $(this).offset().top;
                    if (scrollTop + windowHeight > elementTop + 100) {
                        $(this).addClass('animated');
                    }
                });

                $('.zoom-in').each(function() {
                    var elementTop = $(this).offset().top;
                    if (scrollTop + windowHeight > elementTop + 100) {
                        $(this).addClass('animated');
                    }
                });
            });

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000, 'easeInOutExpo');
                }
            });

            // Enhanced hover effects for service cards
            $('.feature-v1').hover(
                function() {
                    $(this).find('.wrap-icon img').css('transform', 'scale(1.1) rotate(5deg)');
                },
                function() {
                    $(this).find('.wrap-icon img').css('transform', 'scale(1) rotate(0deg)');
                }
            );

            // Portfolio item hover effects
            $('.portfolio-item').hover(
                function() {
                    $(this).find('img').css('transform', 'scale(1.1)');
                },
                function() {
                    $(this).find('img').css('transform', 'scale(1)');
                }
            );

            // Contact form validation and submission
            $('#contactForm').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    message: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    name: {
                        required: "Please enter your name",
                        minlength: "Name must be at least 2 characters"
                    },
                    email: {
                        required: "Please enter your email",
                        email: "Please enter a valid email address"
                    },
                    message: {
                        required: "Please enter your message",
                        minlength: "Message must be at least 10 characters"
                    }
                },
                submitHandler: function(form) {
                    var formData = $(form).serialize();

                    $.ajax({
                        type: 'POST',
                        url: '<?php echo BASE_URL; ?>/php/send-email.php',
                        data: formData,
                        beforeSend: function() {
                            $('.submitting').html('Sending...');
                            $('input[type="submit"]').prop('disabled', true);
                        },
                        success: function(response) {
                            if (response.trim() === 'OK') {
                                $('#form-message-success').fadeIn();
                                $('#form-message-warning').fadeOut();
                                form.reset();
                            } else {
                                $('#form-message-warning').html(response).fadeIn();
                                $('#form-message-success').fadeOut();
                            }
                        },
                        error: function() {
                            $('#form-message-warning').html('Something went wrong. Please try again.').fadeIn();
                            $('#form-message-success').fadeOut();
                        },
                        complete: function() {
                            $('.submitting').html('');
                            $('input[type="submit"]').prop('disabled', false);
                        }
                    });

                    return false;
                }
            });

            // Parallax effect for hero section
            $(window).scroll(function() {
                var scrolled = $(window).scrollTop();
                var parallax = $('.cover-v1');
                var speed = scrolled * 0.5;
                parallax.css('background-position', 'center ' + speed + 'px');
            });

            // Number counter animation
            $('.number-counter').each(function() {
                var $this = $(this);
                var countTo = $this.attr('data-number');

                $({ countNum: $this.text() }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            });

            // Trigger scroll event on page load
            $(window).trigger('scroll');
        });
    </script>

    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>

</body>
</html>
