        </div> <!-- END .unslate_co-site-inner -->

        <!-- 3D Dynamic Footer -->
        <footer class="footer-3d-container">
            <!-- Footer Background Effects -->
            <div class="footer-bg-effects">
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
                <div class="footer-particle"></div>
            </div>

            <!-- Footer Geometric Shapes -->
            <div class="footer-shapes">
                <div class="footer-shape circle"></div>
                <div class="footer-shape square"></div>
                <div class="footer-shape triangle"></div>
            </div>

            <div class="container">
                <div class="row">
                    <!-- Company Info Section -->
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-section-3d">
                            <!-- Footer Logo -->
                            <div class="footer-logo-3d">
                                <a href="<?php echo BASE_URL; ?>/index.php">
                                    <img src="<?php echo IMAGES_URL; ?>/logo.svg" alt="<?php echo getSetting('site_title', SITE_NAME); ?>" class="footer-logo-img">
                                </a>
                            </div>

                            <p class="footer-description">
                                <?php echo getSetting('company_name', SITE_NAME); ?> is a premier digital agency delivering excellence in web development, digital marketing, and creative solutions. We transform businesses through innovative technology and strategic thinking.
                            </p>

                            <div class="footer-contact-info">
                                <div class="footer-contact-item">
                                    <i class="icon-mail"></i>
                                    <a href="mailto:<?php echo getSetting('company_email', SITE_EMAIL); ?>">
                                        <?php echo getSetting('company_email', SITE_EMAIL); ?>
                                    </a>
                                </div>
                                <div class="footer-contact-item">
                                    <i class="icon-phone"></i>
                                    <a href="tel:<?php echo getSetting('company_phone', '+****************'); ?>">
                                        <?php echo getSetting('company_phone', '+****************'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links Section -->
                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-section-3d">
                            <h4 class="footer-title-3d">Quick Links</h4>
                            <ul class="footer-links-3d">
                                <li><a href="<?php echo BASE_URL; ?>/index.php">Home</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/about.php">About Us</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php">Services</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/portfolio.php">Portfolio</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/contact.php">Contact</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Services Section -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-section-3d">
                            <h4 class="footer-title-3d">Our Services</h4>
                            <ul class="footer-links-3d">
                                <li><a href="<?php echo BASE_URL; ?>/services.php#web-development">Web Development</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#digital-marketing">Digital Marketing</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#graphic-design">Graphic Design</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#video-editing">Video Editing</a></li>
                                <li><a href="<?php echo BASE_URL; ?>/services.php#seo">SEO Services</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Social Media Section -->
                    <div class="col-lg-3 col-md-6 mb-4">
                        <div class="footer-section-3d">
                            <h4 class="footer-title-3d">Follow Us</h4>
                            <p class="footer-social-text">Stay connected with us on social media for the latest updates and insights.</p>

                            <!-- Social Media Links -->
                            <div class="footer-social-3d">
                                <?php if (getSetting('facebook_url') && getSetting('facebook_url') !== '#'): ?>
                                <a href="<?php echo getSetting('facebook_url'); ?>" target="_blank" rel="noopener" class="social-link-3d facebook">
                                    <i class="icon-facebook"></i>
                                    <span>Facebook</span>
                                </a>
                                <?php endif; ?>

                                <?php if (getSetting('twitter_url') && getSetting('twitter_url') !== '#'): ?>
                                <a href="<?php echo getSetting('twitter_url'); ?>" target="_blank" rel="noopener" class="social-link-3d twitter">
                                    <i class="icon-twitter"></i>
                                    <span>Twitter</span>
                                </a>
                                <?php endif; ?>

                                <?php if (getSetting('instagram_url') && getSetting('instagram_url') !== '#'): ?>
                                <a href="<?php echo getSetting('instagram_url'); ?>" target="_blank" rel="noopener" class="social-link-3d instagram">
                                    <i class="icon-instagram"></i>
                                    <span>Instagram</span>
                                </a>
                                <?php endif; ?>

                                <?php if (getSetting('linkedin_url') && getSetting('linkedin_url') !== '#'): ?>
                                <a href="<?php echo getSetting('linkedin_url'); ?>" target="_blank" rel="noopener" class="social-link-3d linkedin">
                                    <i class="icon-linkedin"></i>
                                    <span>LinkedIn</span>
                                </a>
                                <?php endif; ?>

                                <?php if (getSetting('youtube_url') && getSetting('youtube_url') !== '#'): ?>
                                <a href="<?php echo getSetting('youtube_url'); ?>" target="_blank" rel="noopener" class="social-link-3d youtube">
                                    <i class="icon-youtube"></i>
                                    <span>YouTube</span>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer Bottom -->
                <div class="footer-bottom-3d">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <p class="footer-copyright-3d">
                                Copyright &copy; <script>document.write(new Date().getFullYear());</script>
                                <?php echo getSetting('company_name', SITE_NAME); ?>. All rights reserved.
                            </p>
                        </div>
                        <div class="col-md-6 text-md-right">
                            <p class="footer-powered-3d">
                                <i class="icon-heart" style="color: #ffd700;"></i>
                                Powered by Excellence in Digital Solutions
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </footer>

        <style>
        /* 3D Dynamic Footer Styles */
        .footer-3d-container {
            position: relative;
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.95), rgba(26, 35, 50, 0.95), rgba(44, 82, 130, 0.9));
            backdrop-filter: blur(15px);
            border-top: 3px solid;
            border-image: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700) 1;
            padding: 80px 0 0;
            overflow: hidden;
            z-index: 10;
        }

        /* Footer Background Effects */
        .footer-bg-effects {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .footer-particle {
            position: absolute;
            width: 3px;
            height: 3px;
            background: #ffd700;
            border-radius: 50%;
            animation: footerParticleFloat 8s ease-in-out infinite;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.4);
        }

        @keyframes footerParticleFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        .footer-particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
        .footer-particle:nth-child(2) { top: 40%; left: 25%; animation-delay: 1s; }
        .footer-particle:nth-child(3) { top: 60%; left: 40%; animation-delay: 2s; }
        .footer-particle:nth-child(4) { top: 30%; left: 60%; animation-delay: 3s; }
        .footer-particle:nth-child(5) { top: 70%; left: 75%; animation-delay: 4s; }
        .footer-particle:nth-child(6) { top: 50%; left: 90%; animation-delay: 5s; }
        .footer-particle:nth-child(7) { top: 80%; left: 15%; animation-delay: 6s; }
        .footer-particle:nth-child(8) { top: 10%; left: 85%; animation-delay: 7s; }

        /* Footer Geometric Shapes */
        .footer-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .footer-shape {
            position: absolute;
            border: 2px solid rgba(255, 215, 0, 0.1);
            animation: footerShapeRotate 12s linear infinite;
        }

        .footer-shape.circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            top: 15%;
            right: 10%;
        }

        .footer-shape.square {
            width: 35px;
            height: 35px;
            top: 60%;
            left: 5%;
            animation-delay: 4s;
        }

        .footer-shape.triangle {
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-bottom: 35px solid rgba(255, 215, 0, 0.1);
            border-radius: 0;
            top: 80%;
            right: 20%;
            animation-delay: 8s;
        }

        @keyframes footerShapeRotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }

        /* Footer Sections */
        .footer-section-3d {
            position: relative;
            padding: 30px 20px;
            background: rgba(26, 35, 50, 0.3);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 215, 0, 0.2);
            transition: all 0.4s ease;
            height: 100%;
        }

        .footer-section-3d:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border-color: rgba(255, 215, 0, 0.4);
        }

        /* Footer Logo */
        .footer-logo-3d {
            text-align: center;
            margin-bottom: 25px;
        }

        .footer-logo-img {
            height: 70px;
            filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.4));
            transition: all 0.3s ease;
        }

        .footer-logo-img:hover {
            transform: scale(1.05);
            filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.6));
        }

        /* Footer Titles */
        .footer-title-3d {
            color: #ffd700;
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 25px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
            position: relative;
        }

        .footer-title-3d::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            border-radius: 2px;
        }

        /* Footer Description */
        .footer-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 25px;
            font-size: 0.95rem;
        }

        /* Footer Contact Info */
        .footer-contact-info {
            margin-top: 20px;
        }

        .footer-contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }

        .footer-contact-item:hover {
            color: #ffd700;
            transform: translateX(5px);
        }

        .footer-contact-item i {
            color: #ffd700;
            margin-right: 12px;
            font-size: 1.1rem;
            width: 20px;
        }

        .footer-contact-item a {
            color: inherit;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer-contact-item a:hover {
            color: #ffd700;
        }

        /* Footer Links */
        .footer-links-3d {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links-3d li {
            margin-bottom: 12px;
        }

        .footer-links-3d a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            padding-left: 15px;
        }

        .footer-links-3d a::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #ffd700;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .footer-links-3d a:hover {
            color: #ffd700;
            transform: translateX(5px);
        }

        .footer-links-3d a:hover::before {
            transform: translateX(3px);
        }

        /* Footer Social Text */
        .footer-social-text {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        /* Social Media Links */
        .footer-social-3d {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .social-link-3d {
            display: flex;
            align-items: center;
            padding: 8px 15px;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.3);
            border-radius: 25px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .social-link-3d:hover {
            background: rgba(255, 215, 0, 0.2);
            border-color: #ffd700;
            color: #ffd700;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
        }

        .social-link-3d i {
            margin-right: 8px;
            font-size: 1.1rem;
        }

        /* Footer Bottom */
        .footer-bottom-3d {
            margin-top: 50px;
            padding: 30px 0;
            border-top: 1px solid rgba(255, 215, 0, 0.2);
            background: rgba(15, 20, 25, 0.5);
        }

        .footer-copyright-3d,
        .footer-powered-3d {
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
            font-size: 0.9rem;
        }

        .footer-powered-3d i {
            margin: 0 5px;
            animation: heartBeat 2s ease-in-out infinite;
        }

        @keyframes heartBeat {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .footer-3d-container {
                padding: 60px 0 0;
            }

            .footer-section-3d {
                margin-bottom: 30px;
                padding: 25px 15px;
            }

            .footer-social-3d {
                justify-content: center;
            }

            .footer-bottom-3d .col-md-6 {
                text-align: center !important;
                margin-bottom: 15px;
            }
        }
        </style>

    </div> <!-- END .unslate_co--site-wrap -->

    <!-- Loader -->
    <div id="unslate_co--overlayer"></div>
    <div class="site-loader-wrap">
        <div class="site-loader"></div>
    </div>

    <!-- JavaScript Files -->
    <script src="<?php echo JS_URL; ?>/vendor/jquery-3.3.1.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/popper.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/bootstrap.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/owl.carousel.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.waypoints.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.easing.1.3.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.animateNumber.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.fancybox.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jquery.validate.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/aos.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/imagesloaded.pkgd.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/isotope.pkgd.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jarallax.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/jarallax-video.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/TweenMax.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/ScrollMagic.min.js"></script>
    <script src="<?php echo JS_URL; ?>/vendor/scrollmagic.animation.gsap.min.js"></script>

    <!-- Main JavaScript -->
    <script src="<?php echo JS_URL; ?>/scripts-dist.js"></script>
    <script src="<?php echo JS_URL; ?>/main.js"></script>

    <!-- Custom JavaScript for ISHTEEAHAR -->
    <script>
        $(document).ready(function() {
            // Initialize AOS (Animate On Scroll)
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                mirror: false
            });

            // Custom scroll animations
            $(window).scroll(function() {
                var scrollTop = $(window).scrollTop();
                var windowHeight = $(window).height();

                $('.fade-in-up').each(function() {
                    var elementTop = $(this).offset().top;
                    if (scrollTop + windowHeight > elementTop + 100) {
                        $(this).addClass('animated');
                    }
                });

                $('.zoom-in').each(function() {
                    var elementTop = $(this).offset().top;
                    if (scrollTop + windowHeight > elementTop + 100) {
                        $(this).addClass('animated');
                    }
                });
            });

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000, 'easeInOutExpo');
                }
            });

            // Enhanced hover effects for service cards
            $('.feature-v1').hover(
                function() {
                    $(this).find('.wrap-icon img').css('transform', 'scale(1.1) rotate(5deg)');
                },
                function() {
                    $(this).find('.wrap-icon img').css('transform', 'scale(1) rotate(0deg)');
                }
            );

            // Portfolio item hover effects
            $('.portfolio-item').hover(
                function() {
                    $(this).find('img').css('transform', 'scale(1.1)');
                },
                function() {
                    $(this).find('img').css('transform', 'scale(1)');
                }
            );

            // Contact form validation and submission
            $('#contactForm').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 2
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    message: {
                        required: true,
                        minlength: 10
                    }
                },
                messages: {
                    name: {
                        required: "Please enter your name",
                        minlength: "Name must be at least 2 characters"
                    },
                    email: {
                        required: "Please enter your email",
                        email: "Please enter a valid email address"
                    },
                    message: {
                        required: "Please enter your message",
                        minlength: "Message must be at least 10 characters"
                    }
                },
                submitHandler: function(form) {
                    var formData = $(form).serialize();

                    $.ajax({
                        type: 'POST',
                        url: '<?php echo BASE_URL; ?>/php/send-email.php',
                        data: formData,
                        beforeSend: function() {
                            $('.submitting').html('Sending...');
                            $('input[type="submit"]').prop('disabled', true);
                        },
                        success: function(response) {
                            if (response.trim() === 'OK') {
                                $('#form-message-success').fadeIn();
                                $('#form-message-warning').fadeOut();
                                form.reset();
                            } else {
                                $('#form-message-warning').html(response).fadeIn();
                                $('#form-message-success').fadeOut();
                            }
                        },
                        error: function() {
                            $('#form-message-warning').html('Something went wrong. Please try again.').fadeIn();
                            $('#form-message-success').fadeOut();
                        },
                        complete: function() {
                            $('.submitting').html('');
                            $('input[type="submit"]').prop('disabled', false);
                        }
                    });

                    return false;
                }
            });

            // Parallax effect for hero section
            $(window).scroll(function() {
                var scrolled = $(window).scrollTop();
                var parallax = $('.cover-v1');
                var speed = scrolled * 0.5;
                parallax.css('background-position', 'center ' + speed + 'px');
            });

            // Number counter animation
            $('.number-counter').each(function() {
                var $this = $(this);
                var countTo = $this.attr('data-number');

                $({ countNum: $this.text() }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            });

            // Trigger scroll event on page load
            $(window).trigger('scroll');
        });
    </script>

    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>

</body>
</html>
 