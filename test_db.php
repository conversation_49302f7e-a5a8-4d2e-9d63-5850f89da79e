<?php
/**
 * Database Connection Test
 * This file tests if the database connection is working properly
 */

// Include configuration
require_once 'config/config.php';

echo "<h1>Database Connection Test</h1>";

try {
    // Test database connection
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test if database exists and has tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠ Database connected but no tables found. You need to import the database.sql file.</p>";
        echo "<p>To import the database:</p>";
        echo "<ol>";
        echo "<li>Open phpMyAdmin (http://localhost/phpmyadmin)</li>";
        echo "<li>Create a database named 'ishteeahar_agency'</li>";
        echo "<li>Import the database.sql file from your project folder</li>";
        echo "</ol>";
    } else {
        echo "<p style='color: green;'>✓ Database tables found:</p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // Test some basic queries
        try {
            $services = getServices();
            echo "<p style='color: green;'>✓ Services function working. Found " . count($services) . " services.</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error testing services function: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/database.php</p>";
    echo "<p>Make sure:</p>";
    echo "<ul>";
    echo "<li>MySQL is running in XAMPP</li>";
    echo "<li>Database name is correct (ishteeahar_agency)</li>";
    echo "<li>Username and password are correct</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ General error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Homepage</a></p>";
?>
