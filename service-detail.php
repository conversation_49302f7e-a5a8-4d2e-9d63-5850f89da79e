<?php
/**
 * ISHTEEAHAR Digital Agency - Service Detail Page
 * 
 * Individual service information and portfolio
 */

// Include configuration and functions
require_once 'config/config.php';

// Get service slug from URL
$service_slug = $_GET['slug'] ?? '';

if (empty($service_slug)) {
    header('Location: services.php');
    exit();
}

// Get service details
$service = getServiceBySlug($service_slug);

if (!$service) {
    header('Location: services.php');
    exit();
}

// Get portfolio items for this service
$portfolio_items = getPortfolioByService($service['id'], 6);

// Set page-specific meta data
$meta = [
    'title' => $service['name'] . ' - ' . getSetting('site_title', SITE_NAME),
    'description' => $service['short_description'],
    'keywords' => strtolower($service['name']) . ', digital agency, ' . getSetting('site_title', SITE_NAME)
];

// Include header
include 'includes/header.php';
?>

            <!-- Page Header -->
            <div class="cover-v1 jarallax" style="height: 60vh; min-height: 400px;">
                <div class="container">
                    <div class="row align-items-center h-100">
                        <div class="col-md-8 mx-auto text-center">
                            <h1 class="heading hero-title text-3d"><?php echo htmlspecialchars($service['name']); ?></h1>
                            <h2 class="subheading fade-in-up"><?php echo htmlspecialchars($service['short_description']); ?></h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Details -->
            <div class="unslate_co--section">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="box-3d p-5 fade-in-up">
                                <div class="text-center mb-5">
                                    <i class="<?php echo htmlspecialchars($service['icon']); ?>" style="font-size: 80px; color: #ff6b6b;"></i>
                                </div>
                                
                                <h2 class="heading-3d text-center mb-4">About This Service</h2>
                                <p class="lead text-center mb-4">
                                    <?php echo htmlspecialchars($service['description']); ?>
                                </p>

                                <!-- Service Features -->
                                <div class="row mt-5">
                                    <div class="col-md-6">
                                        <h4 class="heading-3d mb-3">What We Offer</h4>
                                        <?php
                                        // Define features for each service
                                        $service_features = [
                                            'web-development' => [
                                                'Custom Web Applications',
                                                'Responsive Design',
                                                'E-commerce Solutions',
                                                'API Integration',
                                                'Performance Optimization',
                                                'Security Implementation'
                                            ],
                                            'digital-marketing' => [
                                                'SEO Optimization',
                                                'PPC Campaigns',
                                                'Content Marketing',
                                                'Analytics & Reporting',
                                                'Social Media Strategy',
                                                'Email Marketing'
                                            ],
                                            'wordpress-development' => [
                                                'Custom Themes',
                                                'Plugin Development',
                                                'Site Optimization',
                                                'Maintenance & Support',
                                                'Security Hardening',
                                                'Performance Tuning'
                                            ],
                                            'graphic-design' => [
                                                'Logo Design',
                                                'Brand Identity',
                                                'Marketing Materials',
                                                'Digital Artwork',
                                                'Print Design',
                                                'UI/UX Design'
                                            ],
                                            'video-editing' => [
                                                'Promotional Videos',
                                                'Corporate Content',
                                                'Social Media Clips',
                                                'Motion Graphics',
                                                'Color Correction',
                                                'Audio Enhancement'
                                            ],
                                            'social-media-marketing' => [
                                                'Strategy Development',
                                                'Content Creation',
                                                'Community Management',
                                                'Paid Advertising',
                                                'Influencer Outreach',
                                                'Analytics & Reporting'
                                            ]
                                        ];
                                        
                                        $features = $service_features[$service['slug']] ?? ['Professional Service', 'Quality Delivery', 'Expert Support'];
                                        ?>
                                        <ul class="list-unstyled">
                                            <?php foreach ($features as $feature): ?>
                                            <li class="mb-2">
                                                <i class="icon-check text-success mr-2"></i>
                                                <?php echo htmlspecialchars($feature); ?>
                                            </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <h4 class="heading-3d mb-3">Why Choose Us</h4>
                                        <ul class="list-unstyled">
                                            <li class="mb-2">
                                                <i class="icon-star text-warning mr-2"></i>
                                                Expert team with proven experience
                                            </li>
                                            <li class="mb-2">
                                                <i class="icon-clock text-info mr-2"></i>
                                                Timely delivery and project management
                                            </li>
                                            <li class="mb-2">
                                                <i class="icon-heart text-danger mr-2"></i>
                                                Client-focused approach
                                            </li>
                                            <li class="mb-2">
                                                <i class="icon-trending_up text-success mr-2"></i>
                                                Results-driven solutions
                                            </li>
                                            <li class="mb-2">
                                                <i class="icon-support text-primary mr-2"></i>
                                                Ongoing support and maintenance
                                            </li>
                                            <li class="mb-2">
                                                <i class="icon-dollar text-warning mr-2"></i>
                                                Competitive pricing
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="text-center mt-5">
                                    <a href="contact.php" class="btn btn-custom-3d btn-lg mr-3">Get Started</a>
                                    <a href="portfolio.php?service=<?php echo urlencode($service['slug']); ?>" 
                                       class="btn btn-outline-pill btn-custom-light btn-lg">View Portfolio</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portfolio Section -->
            <?php if (!empty($portfolio_items)): ?>
            <div class="unslate_co--section" style="background: linear-gradient(135deg, #1a1a1a, #0d0d0d);">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal">Our <?php echo htmlspecialchars($service['name']); ?> Work</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row">
                        <?php foreach ($portfolio_items as $index => $item): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="portfolio-item zoom-in" data-aos="zoom-in" data-aos-delay="<?php echo $index * 100; ?>">
                                <div class="overlay">
                                    <span class="wrap-icon icon-link2"></span>
                                    <div class="portfolio-item-content">
                                        <h3><?php echo htmlspecialchars($item['title']); ?></h3>
                                        <p><?php echo htmlspecialchars($item['service_name']); ?></p>
                                        <?php if ($item['client_name']): ?>
                                        <p class="small">Client: <?php echo htmlspecialchars($item['client_name']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div style="height: 250px; background: linear-gradient(135deg, #333, #666); display: flex; align-items: center; justify-content: center; color: #fff; font-size: 18px; border-radius: 10px;">
                                    <div class="text-center">
                                        <i class="icon-image" style="font-size: 48px; opacity: 0.5;"></i>
                                        <div style="margin-top: 10px; font-size: 14px; opacity: 0.7;">
                                            <?php echo htmlspecialchars($item['title']); ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="text-center mt-5">
                        <a href="portfolio.php?service=<?php echo urlencode($service['slug']); ?>" 
                           class="btn btn-custom-3d btn-lg">View All <?php echo htmlspecialchars($service['name']); ?> Projects</a>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Process Section -->
            <div class="unslate_co--section">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal">Our Process</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="box-3d p-4 text-center h-100 fade-in-up" data-aos="fade-up" data-aos-delay="0">
                                <div class="mb-3">
                                    <div class="process-number">1</div>
                                </div>
                                <h4 class="heading-3d">Consultation</h4>
                                <p>We discuss your requirements and understand your goals</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="box-3d p-4 text-center h-100 fade-in-up" data-aos="fade-up" data-aos-delay="100">
                                <div class="mb-3">
                                    <div class="process-number">2</div>
                                </div>
                                <h4 class="heading-3d">Planning</h4>
                                <p>We create a detailed strategy and project timeline</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="box-3d p-4 text-center h-100 fade-in-up" data-aos="fade-up" data-aos-delay="200">
                                <div class="mb-3">
                                    <div class="process-number">3</div>
                                </div>
                                <h4 class="heading-3d">Development</h4>
                                <p>Our team brings your project to life with expertise</p>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="box-3d p-4 text-center h-100 fade-in-up" data-aos="fade-up" data-aos-delay="300">
                                <div class="mb-3">
                                    <div class="process-number">4</div>
                                </div>
                                <h4 class="heading-3d">Delivery</h4>
                                <p>We deliver the final product and provide ongoing support</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="unslate_co--section" style="background: linear-gradient(135deg, #1a1a1a, #0d0d0d);">
                <div class="container">
                    <div class="row">
                        <div class="col-md-8 mx-auto text-center">
                            <div class="box-3d p-5">
                                <h2 class="heading-3d mb-4">Ready to Get Started?</h2>
                                <p class="lead mb-4">
                                    Let's discuss your <?php echo strtolower($service['name']); ?> needs and create 
                                    a solution that drives results for your business.
                                </p>
                                <a href="contact.php" class="btn btn-custom-3d btn-lg mr-3">Start Your Project</a>
                                <a href="services.php" class="btn btn-outline-pill btn-custom-light btn-lg">All Services</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
                .process-number {
                    width: 60px;
                    height: 60px;
                    border-radius: 50%;
                    background: linear-gradient(145deg, #ff6b6b, #ff5252);
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24px;
                    font-weight: bold;
                    margin: 0 auto;
                    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
                }
            </style>

<?php
// Include footer
include 'includes/footer.php';
?>
