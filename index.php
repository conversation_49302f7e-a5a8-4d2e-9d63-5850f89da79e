<?php
/**
 * ISHTEEAHAR Digital Agency - Homepage
 *
 * Main landing page showcasing services, portfolio, and company information
 */

// Include configuration and functions
require_once 'config/config.php';

// Set page-specific meta data
$meta = [
    'title' => getSetting('hero_title', 'ISHTEEHAR') . ' - ' . getSetting('site_tagline', 'Digital Agency Excellence'),
    'description' => getSetting('hero_subtitle', 'We Are A Premier Digital Agency Delivering Excellence In Web Development, Digital Marketing & Creative Solutions'),
    'keywords' => 'digital agency, web development, digital marketing, wordpress development, graphic design, video editing, social media marketing, ISHTEEAHAR'
];

// Get data for the page
$services = getServices();
$featured_portfolio = getAllPortfolio(6, true); // Get 6 featured portfolio items

// Include header
include 'includes/header.php';
?>

<style>
/* 3D Dynamic Background for Index Page */
.index-page-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f1419, #1a2332, #2c5282, #1a365d, #0f1419);
    background-size: 400% 400%;
    animation: dynamicGradientShift 20s ease infinite;
    z-index: -3;
}

@keyframes dynamicGradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 50%; }
}

/* Floating 3D Particles */
.floating-3d-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -2;
    overflow: hidden;
}

.particle-3d {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    animation: float3D 12s linear infinite;
}

@keyframes float3D {
    0% {
        transform: translateY(100vh) translateX(0) rotateZ(0deg) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) translateX(10px) rotateZ(36deg) scale(0.5);
    }
    50% {
        opacity: 1;
        transform: translateY(50vh) translateX(-20px) rotateZ(180deg) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) translateX(30px) rotateZ(324deg) scale(0.8);
    }
    100% {
        transform: translateY(-10vh) translateX(0) rotateZ(360deg) scale(0);
        opacity: 0;
    }
}

/* Geometric Shapes Background */
.geometric-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.shape {
    position: absolute;
    border: 2px solid rgba(0, 0, 0, 0.1);
    animation: rotateShape 15s linear infinite;
}

.shape.triangle {
    width: 0;
    height: 0;
    border-left: 25px solid transparent;
    border-right: 25px solid transparent;
    border-bottom: 43px solid rgba(8, 8, 8, 0.1);
    border-radius: 0;
}

.shape.circle {
    border-radius: 50%;
    width: 50px;
    height: 50px;
}

.shape.square {
    width: 40px;
    height: 40px;
    transform-origin: center;
}

.shape.hexagon {
    width: 60px;
    height: 34px;
    background: rgba(0, 0, 0, 0.1);
    position: relative;
    border: none;
}

.shape.hexagon:before,
.shape.hexagon:after {
    content: "";
    position: absolute;
    width: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
}

.shape.hexagon:before {
    bottom: 100%;
    border-bottom: 17px solid rgba(255, 215, 0, 0.1);
}

.shape.hexagon:after {
    top: 100%;
    border-top: 17px solid rgba(255, 215, 0, 0.1);
}

@keyframes rotateShape {
    0% { transform: rotate(0deg) translateX(0) translateY(0); }
    25% { transform: rotate(90deg) translateX(20px) translateY(-10px); }
    50% { transform: rotate(180deg) translateX(0) translateY(20px); }
    75% { transform: rotate(270deg) translateX(-20px) translateY(-10px); }
    100% { transform: rotate(360deg) translateX(0) translateY(0); }
}

/* Animated Grid Lines */
.grid-lines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
}

.grid-line {
    position: absolute;
    background: linear-gradient(90deg, transparent, #ffd700, transparent);
    animation: gridPulse 8s ease-in-out infinite;
}

.grid-line.horizontal {
    width: 100%;
    height: 1px;
}

.grid-line.vertical {
    width: 1px;
    height: 100%;
}

@keyframes gridPulse {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}


.particle-3d:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 10s; }
.particle-3d:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 12s; }
.particle-3d:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 11s; }
.particle-3d:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 13s; }
.particle-3d:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 9s; }
.particle-3d:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 14s; }
.particle-3d:nth-child(7) { left: 70%; animation-delay: 6s; animation-duration: 10s; }
.particle-3d:nth-child(8) { left: 80%; animation-delay: 7s; animation-duration: 12s; }
.particle-3d:nth-child(9) { left: 90%; animation-delay: 8s; animation-duration: 11s; }
.particle-3d:nth-child(10) { left: 15%; animation-delay: 9s; animation-duration: 13s; }
.particle-3d:nth-child(11) { left: 25%; animation-delay: 10s; animation-duration: 10s; }
.particle-3d:nth-child(12) { left: 35%; animation-delay: 11s; animation-duration: 12s; }
.particle-3d:nth-child(13) { left: 45%; animation-delay: 12s; animation-duration: 11s; }
.particle-3d:nth-child(14) { left: 55%; animation-delay: 13s; animation-duration: 14s; }
.particle-3d:nth-child(15) { left: 65%; animation-delay: 14s; animation-duration: 9s; }


.shape:nth-child(1) { top: 10%; left: 5%; animation-delay: 0s; }
.shape:nth-child(2) { top: 20%; right: 10%; animation-delay: 2s; }
.shape:nth-child(3) { top: 60%; left: 15%; animation-delay: 4s; }
.shape:nth-child(4) { top: 80%; right: 20%; animation-delay: 6s; }
.shape:nth-child(5) { top: 40%; left: 80%; animation-delay: 8s; }
.shape:nth-child(6) { top: 70%; left: 70%; animation-delay: 10s; }


.grid-line:nth-child(1) { top: 20%; animation-delay: 0s; }
.grid-line:nth-child(2) { top: 40%; animation-delay: 2s; }
.grid-line:nth-child(3) { top: 60%; animation-delay: 4s; }
.grid-line:nth-child(4) { top: 80%; animation-delay: 6s; }
.grid-line:nth-child(5) { left: 20%; animation-delay: 1s; }
.grid-line:nth-child(6) { left: 40%; animation-delay: 3s; }
.grid-line:nth-child(7) { left: 60%; animation-delay: 5s; }
.grid-line:nth-child(8) { left: 80%; animation-delay: 7s; }

/* Professional 3D Stylish Carousel */
.hero-carousel-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
    background: transparent;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-3d {
    position: relative;
    width: 100%;
    height: 100%;
    perspective: 1500px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform-style: preserve-3d;
}

.carousel-slide {
    position: absolute;
    width: 85%;
    max-width: 1200px;
    height: 75%;
    background: linear-gradient(145deg,
      
        rgba(0, 10, 48, 0.9));
    border-radius: 30px;
    backdrop-filter: blur(20px);
    border: 2px solid #ffd700;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 80px 60px;
    opacity: 0;

    box-shadow:
        0 40px 80px rgba(0, 0, 0, 0.5),
        0 25px 50px rgba(15, 25, 45, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px #ffd700;
    overflow: hidden;
}

.carousel-slide::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 33px;
    z-index: -1;
    animation: slideBorderPulse 6s ease-in-out infinite;
}

.carousel-slide::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle,
        rgba(255, 215, 0, 0.1) 0%,
        rgba(255, 237, 78, 0.05) 50%,
        transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.8s ease;
    z-index: 0;
}

@keyframes animatedYellowBorder {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideBorderPulse {
    0% { opacity: 0.7; transform: scale(1); }
    25% { opacity: 0.85; transform: scale(1.01); }
    50% { opacity: 1; transform: scale(1.02); }
    75% { opacity: 0.85; transform: scale(1.01); }
    100% { opacity: 0.7; transform: scale(1); }
}

@keyframes professionalBorderPulse {
    0% { transform: scale(1); opacity: 0.8; }
    25% { transform: scale(1.01); opacity: 0.9; }
    50% { transform: scale(1.02); opacity: 1; }
    75% { transform: scale(1.01); opacity: 0.9; }
    100% { transform: scale(1); opacity: 0.8; }
}

.carousel-slide.active {
    opacity: 1;
    transform: translateZ(0) rotateY(0deg) scale(1);
    z-index: 10;
    box-shadow:
        0 50px 100px rgba(0, 0, 0, 0.6),
        0 30px 60px rgba(30, 58, 95, 0.5),
        0 0 50px rgba(255, 215, 0, 0.2);
}

.carousel-slide.active::after {
    width: 300%;
    height: 300%;
}

.carousel-slide.prev {
    opacity: 0.4;
    transform: translateZ(-300px) rotateY(-35deg) scale(0.8) translateX(-60%);
    z-index: 5;
    filter: blur(1px);
}

.carousel-slide.next {
    opacity: 0.4;
    transform: translateZ(-300px) rotateY(35deg) scale(0.8) translateX(60%);
    z-index: 5;
    filter: blur(1px);
}

.slide-content {
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
}

.slide-content h1 {
    font-size: 4rem;
    font-weight: 900;
    background: linear-gradient(135deg,
rgb(255, 238, 0) 0%,

rgb(255, 255, 255) 75%,
rgb(255, 230, 0) 100%);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 25px;
    animation: professionalTextGlow 8s ease-in-out infinite alternate,
               professionalGradientShift 15s ease-in-out infinite;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
    letter-spacing: 2px;
    line-height: 1.1;
}

@keyframes professionalTextGlow {
    0% {
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.6));
        transform: scale(1.01);
    }
    100% {
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3));
        transform: scale(1);
    }
}

@keyframes professionalGradientShift {
    0% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 100% 100%; }
    75% { background-position: 0% 100%; }
    100% { background-position: 0% 50%; }
}

.slide-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffd700;
    margin-bottom: 25px;
    text-shadow: 0 0 25px rgba(255, 215, 0, 0.4);
    letter-spacing: 1px;
    opacity: 0.95;
}

.slide-content p {
    font-size: 1.2rem;
    color: rgba(0, 0, 0, 0.9);
    margin-bottom: 45px;
    line-height: 1.7;
    font-weight: 400;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.carousel-controls {
    position: absolute;
    bottom: 60px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 30px;
    z-index: 20;
}

.carousel-btn {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(145deg,
        rgba(15, 25, 45, 0.95),
        rgba(25, 35, 65, 0.9));
    border: none;
    color: rgba(15, 25, 45, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(15px);
    font-size: 24px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.3),
        0 5px 15px rgba(15, 25, 45, 0.4);
}

.carousel-btn::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: conic-gradient(from 0deg, #ffd700, #ffed4e, #ffd700, #ffed4e, #ffd700);
    border-radius: 50%;
    z-index: -1;
    animation: animatedYellowBorder 6s linear infinite;
}

.carousel-btn:hover {
    transform: scale(1.15) translateY(-5px);
    color: rgba(15, 25, 45, 1);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        0 10px 25px rgba(255, 215, 0, 0.3);
}

.carousel-btn:active {
    transform: scale(1.05) translateY(-2px);
}

.carousel-indicators {
    position: absolute;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
    z-index: 20;
    padding: 15px 25px;
    background: rgba(15, 25, 45, 0.8);
    border-radius: 50px;
    backdrop-filter: blur(15px);
    border: none;
}

.carousel-indicators::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(from 0deg,rgb(255, 238, 0), #ffed4e, #ffd700, #ffed4e, #ffd700);
    border-radius: 52px;
    z-index: -1;
    animation: animatedYellowBorder 10s linear infinite;
}

.indicator {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: rgba(15, 25, 45, 0.8);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: none;
}

.indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(from 0deg, #ffd700, #ffed4e, #ffd700);
    border-radius: 50%;
    z-index: -1;
    animation: animatedYellowBorder 4s linear infinite;
}

.indicator.active {
    background: rgba(15, 25, 45, 1);
    transform: scale(1.4);
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.4);
}

.indicator:hover:not(.active) {
    background: rgba(25, 35, 65, 0.9);
    transform: scale(1.2);
    border-color: #ffed4e;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #ffd700;
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
}

.floating-element:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.floating-element:nth-child(2) { top: 60%; left: 20%; animation-delay: 1s; }
.floating-element:nth-child(3) { top: 30%; right: 15%; animation-delay: 2s; }
.floating-element:nth-child(4) { top: 70%; right: 25%; animation-delay: 3s; }
.floating-element:nth-child(5) { top: 40%; left: 80%; animation-delay: 4s; }

/* Clean headings without effects */
.gsap-reveal .cover {
    background: transparent !important;
}

/* Simple clean headings */
.heading-h2, .heading-3d {
    color:rgb(0, 81, 255) !important;
    font-weight: 700 !important;
    filter: none !important;
    backdrop-filter: none !important;
    text-shadow: none !important;
    background: none !important;
    -webkit-text-fill-color:rgb(0, 0, 0) !important;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slide-content h1 { font-size: 2.5rem; }
    .slide-content h2 { font-size: 1.2rem; }
    .slide-content p { font-size: 1rem; }
    .carousel-slide { padding: 40px 20px; }
}

/* Featured Portfolio Animated Borders */
.portfolio-item-3d {
    position: relative;
    background: transparent;
    border-radius: 20px;
    overflow: hidden;
    margin-bottom: 30px;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.portfolio-item-3d:hover {
    transform: rotateY(5deg) rotateX(3deg) translateZ(15px);
}

.portfolio-border-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 350px;
    background: #1a2332;
    border-radius: 20px;
    overflow: hidden;
}

.portfolio-border-wrapper::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, #ffd700, transparent, #ffed4e, transparent, #ffd700, transparent);
    z-index: 1;
    animation: portfolioBorderRotate 6s linear infinite;
}

.portfolio-border-wrapper::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 180deg, transparent, #ffed4e, transparent, #ffd700, transparent, #ffed4e, transparent);
    z-index: 1;
    animation: portfolioBorderRotate 6s linear infinite reverse;
}

@keyframes portfolioBorderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.portfolio-inner-content {
    position: relative;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.9));
    border-radius: 18px;
    z-index: 2;
    height: 100%;
    min-height: 346px;
    margin: 2px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
}

.portfolio-image-section {
    height: 200px;
    background: linear-gradient(135deg, #2c5282, #1a365d);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    color: #fff;
    font-size: 18px;
}

.portfolio-image-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    animation: portfolioShine 3s ease-in-out infinite;
}

@keyframes portfolioShine {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: -100%; }
}

.portfolio-content-section {
    padding: 25px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
}

.portfolio-title-3d {
    color: #ffd700;
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.portfolio-category-3d {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 15px;
}

.portfolio-overlay-3d {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 215, 0, 0.9), rgba(255, 237, 78, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 10;
    backdrop-filter: blur(5px);
}

.portfolio-item-3d:hover .portfolio-overlay-3d {
    opacity: 1;
}

.portfolio-overlay-content {
    text-align: center;
    color: #000;
    font-weight: bold;
}

.portfolio-overlay-content i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.portfolio-overlay-content h4 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.portfolio-overlay-content p {
    font-size: 1rem;
    margin: 0;
}

/* Pulse Animation for Portfolio Items */
.portfolio-pulse {
    position: relative;
}

.portfolio-pulse::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    border-radius: 25px;
    z-index: -1;
    animation: portfolioPulse 3s ease-in-out infinite;
    opacity: 0;
}

@keyframes portfolioPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.3;
    }
}

.portfolio-item-3d:hover.portfolio-pulse::before {
    animation-play-state: paused;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .portfolio-border-wrapper {
        min-height: 300px;
    }

    .portfolio-inner-content {
        min-height: 296px;
    }

    .portfolio-image-section {
        height: 160px;
        font-size: 16px;
    }

    .portfolio-content-section {
        padding: 20px 15px;
    }

    .portfolio-title-3d {
        font-size: 1.1rem;
    }
}
</style>

            <!-- 3D Dynamic Background -->
            <div class="index-page-bg"></div>

            <!-- Floating 3D Particles -->
            <div class="floating-3d-particles">
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
                <div class="particle-3d"></div>
            </div>

            <!-- Geometric Shapes -->
            <div class="geometric-shapes">
                <div class="shape circle"></div>
                <div class="shape square"></div>
                <div class="shape triangle"></div>
                <div class="shape hexagon"></div>
                <div class="shape circle"></div>
                <div class="shape square"></div>
            </div>

            <!-- Animated Grid Lines -->
            <div class="grid-lines">
                <div class="grid-line horizontal"></div>
                <div class="grid-line horizontal"></div>
                <div class="grid-line horizontal"></div>
                <div class="grid-line horizontal"></div>
                <div class="grid-line vertical"></div>
                <div class="grid-line vertical"></div>
                <div class="grid-line vertical"></div>
                <div class="grid-line vertical"></div>
            </div>

            <!-- 3D Dynamic Hero Carousel -->
            <div class="hero-carousel-container" id="home-section">
                <!-- Floating Elements -->
                <div class="floating-elements">
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                </div>

                <div class="carousel-3d" id="heroCarousel">
                    <!-- Slide 1 -->
                    <div class="carousel-slide active">
                        <div class="slide-content">
                            <h1>ISHTEEHAR</h1>
                            <h2>Digital Agency Excellence</h2>
                            <p style="colo">We Are A Premier Digital Agency Delivering Excellence In Web Development, Digital Marketing & Creative Solutions</p>
                            <div class="mt-4">
                                <a href="#services-section" class="btn btn-custom-3d btn-lg mr-3">Our Services</a>
                                <a href="#portfolio-section" class="btn btn-outline-pill btn-custom-light btn-lg">View Portfolio</a>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 2 -->
                    <div class="carousel-slide">
                        <div class="slide-content">
                            <h1>Web Development</h1>
                            <h2>Modern & Responsive Solutions</h2>
                            <p>Custom websites and web applications built with cutting-edge technology, optimized for performance and user experience</p>
                            <div class="mt-4">
                                <a href="services.php" class="btn btn-custom-3d btn-lg mr-3">Learn More</a>
                                <a href="portfolio.php?service=web-development" class="btn btn-outline-pill btn-custom-light btn-lg">View Projects</a>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 3 -->
                    <div class="carousel-slide">
                        <div class="slide-content">
                            <h1>Digital Marketing</h1>
                            <h2>Strategic Growth Solutions</h2>
                            <p>Comprehensive digital marketing strategies including SEO, PPC, social media marketing, and content creation to boost your online presence</p>
                            <div class="mt-4">
                                <a href="services.php" class="btn btn-custom-3d btn-lg mr-3">Get Started</a>
                                <a href="contact.php" class="btn btn-outline-pill btn-custom-light btn-lg">Contact Us</a>
                            </div>
                        </div>
                    </div>

                    <!-- Slide 4 -->
                    <div class="carousel-slide">
                        <div class="slide-content">
                            <h1>Creative Design</h1>
                            <h2>Visual Excellence</h2>
                            <p>Professional graphic design, branding, and video editing services that capture your brand's essence and engage your audience</p>
                            <div class="mt-4">
                                <a href="portfolio.php" class="btn btn-custom-3d btn-lg mr-3">View Work</a>
                                <a href="contact.php" class="btn btn-outline-pill btn-custom-light btn-lg">Start Project</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Carousel Controls -->
                <div class="carousel-controls">
                    <button class="carousel-btn" id="prevBtn">
                        <i class="icon-chevron-left"></i>
                    </button>
                    <button class="carousel-btn" id="nextBtn">
                        <i class="icon-chevron-right"></i>
                    </button>
                </div>

                <!-- Carousel Indicators -->
                <div class="carousel-indicators">
                    <div class="indicator active" data-slide="0"></div>
                    <div class="indicator" data-slide="1"></div>
                    <div class="indicator" data-slide="2"></div>
                    <div class="indicator" data-slide="3"></div>
                </div>

                <!-- Scroll Down Arrow -->
                <a href="#services-section" class="mouse-wrap smoothscroll">
                    <span class="mouse">
                        <span class="scroll"></span>
                    </span>
                    <span class="mouse-label">Discover</span>
                </a>
            </div>

            <script>
            // 3D Carousel JavaScript
            document.addEventListener('DOMContentLoaded', function() {
                const slides = document.querySelectorAll('.carousel-slide');
                const indicators = document.querySelectorAll('.indicator');
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');
                let currentSlide = 0;
                const totalSlides = slides.length;

                function updateCarousel() {
                    slides.forEach((slide, index) => {
                        slide.classList.remove('active', 'prev', 'next');

                        if (index === currentSlide) {
                            slide.classList.add('active');
                        } else if (index === (currentSlide - 1 + totalSlides) % totalSlides) {
                            slide.classList.add('prev');
                        } else if (index === (currentSlide + 1) % totalSlides) {
                            slide.classList.add('next');
                        }
                    });

                    indicators.forEach((indicator, index) => {
                        indicator.classList.toggle('active', index === currentSlide);
                    });
                }

                function nextSlide() {
                    currentSlide = (currentSlide + 1) % totalSlides;
                    updateCarousel();
                }

                function prevSlide() {
                    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                    updateCarousel();
                }

                // Event listeners
                nextBtn.addEventListener('click', nextSlide);
                prevBtn.addEventListener('click', prevSlide);

                indicators.forEach((indicator, index) => {
                    indicator.addEventListener('click', () => {
                        currentSlide = index;
                        updateCarousel();
                    });
                });

                // Auto-play carousel
                setInterval(nextSlide, 5000);

                // Initialize
                updateCarousel();
            });
            </script>
            <!-- END Hero Carousel -->

            <!-- Services Section -->
            <div class="unslate_co--section" id="services-section">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal">Our Services</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row gutter-v3">
                        <?php if (!empty($services)): ?>
                            <?php foreach ($services as $index => $service): ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="feature-v1 box-3d fade-in-up" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                                    <div class="wrap-icon mb-3">
                                        <i class="<?php echo htmlspecialchars($service['icon']); ?>" style="font-size: 45px; color: #ff6b6b;"></i>
                                    </div>
                                    <h3 class="heading-3d"><?php echo htmlspecialchars($service['name']); ?></h3>
                                    <p><?php echo htmlspecialchars($service['short_description']); ?></p>
                                    <div class="mt-3">
                                        <a href="service-detail.php?slug=<?php echo urlencode($service['slug']); ?>"
                                           class="btn btn-sm btn-custom-3d">Learn More</a>
                                        <a href="portfolio.php?service=<?php echo urlencode($service['slug']); ?>"
                                           class="btn btn-sm btn-outline-pill btn-custom-light ml-2">View Portfolio</a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <!-- Fallback content if no services in database -->
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="feature-v1 box-3d" data-aos="fade-up" data-aos-delay="0">
                                    <div class="wrap-icon mb-3">
                                        <i class="icon-code" style="font-size: 45px; color:rgb(255, 217, 4);"></i>
                                    </div>
                                    <h3>Web Development</h3>
                                    <p>Custom websites and web applications built with cutting-edge technology</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="feature-v1 box-3d" data-aos="fade-up" data-aos-delay="100">
                                    <div class="wrap-icon mb-3">
                                        <i class="icon-trending_up" style="font-size: 45px; color:rgb(209, 209, 209);"></i>
                                    </div>
                                    <h3>Digital Marketing</h3>
                                    <p>Strategic online marketing solutions to grow your business</p>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="feature-v1 box-3d" data-aos="fade-up" data-aos-delay="200">
                                    <div class="wrap-icon mb-3">
                                        <i class="icon-wordpress" style="font-size: 45px; color:rgb(185, 185, 185);"></i>
                                    </div>
                                    <h3>WordPress Development</h3>
                                    <p>Custom WordPress solutions for businesses of all sizes</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <!-- END Services Section -->

            <!-- Portfolio Section -->
            <div class="unslate_co--section" id="portfolio-section">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal"><i class=" heading-icon bounce"></i> Portfolio</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="portfolio-wrapper">
                        <div id="posts" class="row gutter-isotope-item">
                            <?php if (!empty($featured_portfolio)): ?>
                                <?php foreach ($featured_portfolio as $index => $item): ?>
                                <div class="col-sm-6 col-md-6 col-lg-4 isotope-mb-2">
                                    <div class="portfolio-item-3d portfolio-pulse zoom-in" data-aos="zoom-in" data-aos-delay="<?php echo ($index % 3) * 100; ?>">
                                        <div class="portfolio-border-wrapper">
                                            <div class="portfolio-inner-content">
                                                <div class="portfolio-image-section">
                                                    <div>
                                                        <i class="icon-image" style="font-size: 3rem; opacity: 0.5; color: #ffd700; margin-bottom: 10px; display: block;"></i>
                                                        Portfolio Image
                                                    </div>
                                                </div>

                                                <div class="portfolio-content-section">
                                                    <h3 class="portfolio-title-3d"><?php echo htmlspecialchars($item['title']); ?></h3>
                                                    <p class="portfolio-category-3d"><?php echo htmlspecialchars($item['service_name']); ?></p>
                                                    <?php if (!empty($item['short_description'])): ?>
                                                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">
                                                        <?php echo htmlspecialchars(substr($item['short_description'], 0, 80)) . (strlen($item['short_description']) > 80 ? '...' : ''); ?>
                                                    </p>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- Hover Overlay -->
                                                <div class="portfolio-overlay-3d">
                                                    <div class="portfolio-overlay-content">
                                                        <i class="icon-link2"></i>
                                                        <h4>View Project</h4>
                                                        <p>Click to see details</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <!-- Fallback portfolio items -->
                                <div class="col-sm-6 col-md-6 col-lg-4 isotope-mb-2">
                                    <div class="portfolio-item-3d portfolio-pulse zoom-in" data-aos="zoom-in">
                                        <div class="portfolio-border-wrapper">
                                            <div class="portfolio-inner-content">
                                                <div class="portfolio-image-section">
                                                    <div>
                                                        <i class="icon-image" style="font-size: 3rem; opacity: 0.5; color: #ffd700; margin-bottom: 10px; display: block;"></i>
                                                        Portfolio Image
                                                    </div>
                                                </div>

                                                <div class="portfolio-content-section">
                                                    <h3 class="portfolio-title-3d">E-Commerce Platform</h3>
                                                    <p class="portfolio-category-3d">Web Development</p>
                                                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">
                                                        Modern e-commerce solution with advanced features...
                                                    </p>
                                                </div>

                                                <!-- Hover Overlay -->
                                                <div class="portfolio-overlay-3d">
                                                    <div class="portfolio-overlay-content">
                                                        <i class="icon-link2"></i>
                                                        <h4>View Project</h4>
                                                        <p>Click to see details</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6 col-md-6 col-lg-4 isotope-mb-2">
                                    <div class="portfolio-item-3d portfolio-pulse zoom-in" data-aos="zoom-in" data-aos-delay="100">
                                        <div class="portfolio-border-wrapper">
                                            <div class="portfolio-inner-content">
                                                <div class="portfolio-image-section">
                                                    <div>
                                                        <i class="icon-image" style="font-size: 3rem; opacity: 0.5; color: #ffd700; margin-bottom: 10px; display: block;"></i>
                                                        Portfolio Image
                                                    </div>
                                                </div>

                                                <div class="portfolio-content-section">
                                                    <h3 class="portfolio-title-3d">Brand Identity Design</h3>
                                                    <p class="portfolio-category-3d">Graphic Design</p>
                                                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">
                                                        Complete brand identity with logo and guidelines...
                                                    </p>
                                                </div>

                                                <!-- Hover Overlay -->
                                                <div class="portfolio-overlay-3d">
                                                    <div class="portfolio-overlay-content">
                                                        <i class="icon-link2"></i>
                                                        <h4>View Project</h4>
                                                        <p>Click to see details</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6 col-md-6 col-lg-4 isotope-mb-2">
                                    <div class="portfolio-item-3d portfolio-pulse zoom-in" data-aos="zoom-in" data-aos-delay="200">
                                        <div class="portfolio-border-wrapper">
                                            <div class="portfolio-inner-content">
                                                <div class="portfolio-image-section">
                                                    <div>
                                                        <i class="icon-image" style="font-size: 3rem; opacity: 0.5; color: #ffd700; margin-bottom: 10px; display: block;"></i>
                                                        Portfolio Image
                                                    </div>
                                                </div>

                                                <div class="portfolio-content-section">
                                                    <h3 class="portfolio-title-3d">Digital Marketing Campaign</h3>
                                                    <p class="portfolio-category-3d">Digital Marketing</p>
                                                    <p style="color: rgba(255, 255, 255, 0.6); font-size: 0.9rem; margin: 0;">
                                                        Comprehensive digital marketing strategy and execution...
                                                    </p>
                                                </div>

                                                <!-- Hover Overlay -->
                                                <div class="portfolio-overlay-3d">
                                                    <div class="portfolio-overlay-content">
                                                        <i class="icon-link2"></i>
                                                        <h4>View Project</h4>
                                                        <p>Click to see details</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="text-center mt-5">
                            <a href="portfolio.php" class="btn btn-custom-3d btn-lg">View All Portfolio</a>
                        </div>
                    </div>

  </div>
                    </div>
            <!-- END Portfolio Section -->
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
            <!-- About Section -->

                <div class="container">

                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal"><i class=""></i><?php echo getSetting('about_title', 'About ISHTEEHAR'); ?></span>
                        </h2>

                    </div>


                    <div class="row mt-5 justify-content-center">
                        <div class="col-lg-8 text-center">
                            <div class="box-3d p-5 fade-in-up">
                                <h3 class="mb-4 heading-h3 heading-3d">
                                    <span class="gsap-reveal">Excellence in Digital Solutions</span>
                                </h3>
                                <p class="lead gsap-reveal">
                                    <?php echo getSetting('about_description', 'We are a full-service digital agency committed to delivering exceptional results for our clients. With expertise spanning web development, digital marketing, and creative services, we transform ideas into powerful digital experiences that drive business growth.'); ?>
                                </p>
                                <div class="mt-4">
                                    <a href="about.php" class="btn btn-custom-3d btn-lg">Learn More About Us</a>
                                </div>
                            </div>

            <!-- END About Section -->

<?php
// Include footer
include 'includes/footer.php';
?>
