<?php
/**
 * Database Setup Script
 * This script will create the database and tables automatically
 */

// Database configuration (without database name for initial connection)
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'ishteeahar_agency';

echo "<h1>Database Setup Script</h1>";

try {
    // Connect to MySQL server (without specifying database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✓ Connected to MySQL server</p>";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database`");
    echo "<p style='color: green;'>✓ Database '$database' created/verified</p>";
    
    // Use the database
    $pdo->exec("USE `$database`");
    
    // Read and execute the SQL file
    $sql_file = 'database.sql';
    if (file_exists($sql_file)) {
        $sql_content = file_get_contents($sql_file);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql_content)));
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Ignore table already exists errors
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        throw $e;
                    }
                }
            }
        }
        
        echo "<p style='color: green;'>✓ Database tables created/updated successfully</p>";
        
        // Verify tables were created
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p><strong>Tables created:</strong></p>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
        // Test data insertion
        $stmt = $pdo->query("SELECT COUNT(*) FROM services");
        $service_count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Found $service_count services in database</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM site_settings");
        $settings_count = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Found $settings_count site settings in database</p>";
        
        echo "<h2 style='color: green;'>Setup Complete!</h2>";
        echo "<p>Your database has been set up successfully. You can now:</p>";
        echo "<ul>";
        echo "<li><a href='index.php'>Visit your homepage</a></li>";
        echo "<li><a href='test_db.php'>Test database connection</a></li>";
        echo "<li><a href='phpinfo.php'>Check PHP configuration</a></li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>✗ database.sql file not found</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
    echo "<p>Please make sure:</p>";
    echo "<ul>";
    echo "<li>XAMPP MySQL service is running</li>";
    echo "<li>MySQL username and password are correct</li>";
    echo "<li>You have permission to create databases</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}
?>
