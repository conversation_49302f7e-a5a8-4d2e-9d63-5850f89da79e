<?php
/**
 * Enhanced <PERSON>ail Handler for ISHTEEAHAR Digital Agency
 *
 * Handles contact form submissions with database storage and email notifications
 */

// Include configuration
require_once '../config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// Initialize response
$response = ['success' => false, 'message' => ''];

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method.');
    }

    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Security token validation failed.');
    }

    // Sanitize and validate input
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');

    // Validation
    $errors = [];

    if (empty($name) || strlen($name) < 2) {
        $errors[] = 'Name is required and must be at least 2 characters.';
    }

    if (empty($email) || !isValidEmail($email)) {
        $errors[] = 'A valid email address is required.';
    }

    if (empty($message) || strlen($message) < 10) {
        $errors[] = 'Message is required and must be at least 10 characters.';
    }

    if (!empty($errors)) {
        throw new Exception(implode(' ', $errors));
    }

    // Save to database
    $contact_data = [
        'name' => $name,
        'email' => $email,
        'subject' => $subject ?: 'Contact Form Submission',
        'message' => $message
    ];

    $message_id = saveContactMessage($contact_data);

    // Prepare email
    $email_subject = $subject ?: 'New Contact Form Submission - ISHTEEAHAR';
    $email_message = "
    <html>
    <head>
        <title>{$email_subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #000; color: #fff; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .footer { background: #333; color: #fff; padding: 10px; text-align: center; font-size: 12px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #555; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Contact Form Submission</h2>
                <p>ISHTEEAHAR Digital Agency</p>
            </div>
            <div class='content'>
                <div class='field'>
                    <div class='label'>Name:</div>
                    <div>{$name}</div>
                </div>
                <div class='field'>
                    <div class='label'>Email:</div>
                    <div>{$email}</div>
                </div>
                <div class='field'>
                    <div class='label'>Subject:</div>
                    <div>{$email_subject}</div>
                </div>
                <div class='field'>
                    <div class='label'>Message:</div>
                    <div>" . nl2br($message) . "</div>
                </div>
                <div class='field'>
                    <div class='label'>Submitted:</div>
                    <div>" . date('F j, Y \a\t g:i A') . "</div>
                </div>
                <div class='field'>
                    <div class='label'>IP Address:</div>
                    <div>" . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown') . "</div>
                </div>
            </div>
            <div class='footer'>
                <p>This email was sent from the contact form on " . SITE_URL . "</p>
                <p>Message ID: {$message_id}</p>
            </div>
        </div>
    </body>
    </html>
    ";

    // Send email notification
    $to = getSetting('company_email', '<EMAIL>'); // Change this to your email
    $email_sent = sendEmailNotification($to, $email_subject, $email_message, $name, $email);

    if ($email_sent) {
        $response['success'] = true;
        $response['message'] = 'Thank you for your message! We will get back to you soon.';
    } else {
        $response['success'] = true;
        $response['message'] = 'Your message was saved but there was an issue sending the email notification.';
    }

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
    logError($e->getMessage(), __FILE__, __LINE__);
}

// Return JSON response
echo json_encode($response);
?>