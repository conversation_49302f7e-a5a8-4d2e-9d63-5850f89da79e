<?php
/**
 * ISHTEEAHAR Digital Agency - About Page
 *
 * Company information, mission, vision, and team
 */

// Include configuration and functions
require_once 'config/config.php';

// Set page-specific meta data
$meta = [
    'title' => 'About Us - ' . getSetting('site_title', SITE_NAME),
    'description' => 'Learn about ' . getSetting('company_name', SITE_NAME) . ', our mission, vision, and the expert team behind our digital agency success.',
    'keywords' => 'about us, digital agency, team, mission, vision, company, ISHTEEAHAR'
];

// Include header
include 'includes/header.php';
?>

<style>
/* 3D Dynamic Background */
.about-page-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0f1419, #1a2332, #2c5282, #1a365d);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.floating-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: #ffd700;
    border-radius: 50%;
    animation: floatParticle 8s linear infinite;
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; animation-duration: 6s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; animation-duration: 8s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; animation-duration: 7s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; animation-duration: 9s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; animation-duration: 6s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; animation-duration: 8s; }
.particle:nth-child(7) { left: 70%; animation-delay: 6s; animation-duration: 7s; }
.particle:nth-child(8) { left: 80%; animation-delay: 7s; animation-duration: 9s; }
.particle:nth-child(9) { left: 90%; animation-delay: 8s; animation-duration: 6s; }

/* 3D Dynamic Cards */
.dynamic-3d-card {
    position: relative;
    background: transparent;
    border-radius: 20px;
    padding: 0;
    margin-bottom: 30px;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.dynamic-3d-card:hover {
    transform: rotateY(10deg) rotateX(5deg) translateZ(20px);
}

.card-3d-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 350px;
    perspective: 1000px;
}

.card-3d-inner {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.9));
    border-radius: 20px;
    padding: 40px 30px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 15px 35px rgba(30, 58, 95, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
    transition: all 0.4s ease;
}

.card-3d-inner::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ffd700, transparent, #ffed4e, transparent, #ffd700);
    border-radius: 22px;
    z-index: -1;
    animation: borderFlow 4s linear infinite;
}

@keyframes borderFlow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.card-3d-inner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    z-index: 0;
}

.dynamic-3d-card:hover .card-3d-inner::after {
    width: 200%;
    height: 200%;
}

.card-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.card-icon {
    font-size: 4rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 25px;
    display: block;
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% { transform: scale(1); filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.3)); }
    50% { transform: scale(1.1); filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.6)); }
}

.card-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 20px;
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.card-text {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 1rem;
}

/* Team Member 3D Cards */
.team-3d-card {
    position: relative;
    background: transparent;
    border-radius: 20px;
    padding: 0;
    margin-bottom: 30px;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 400px;
}

.team-3d-card:hover {
    transform: rotateY(-10deg) rotateX(5deg) translateZ(30px);
}

.team-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.9));
    border-radius: 20px;
    padding: 30px 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.3),
        0 15px 35px rgba(30, 58, 95, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.team-card-inner::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, #ffd700, transparent, #ffed4e, transparent);
    animation: teamBorderRotate 6s linear infinite;
    z-index: -1;
}

@keyframes teamBorderRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.team-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
    transition: all 0.4s ease;
}

.team-3d-card:hover .team-photo {
    transform: scale(1.1) rotateZ(5deg);
    box-shadow: 0 15px 40px rgba(255, 215, 0, 0.5);
}

.team-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 10px;
}

.team-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 15px;
}

.team-description {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Counter 3D Cards */
.counter-3d-card {
    position: relative;
    background: transparent;
    border-radius: 15px;
    padding: 0;
    margin-bottom: 30px;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    height: 200px;
}

.counter-3d-card:hover {
    transform: translateY(-10px) rotateX(10deg) rotateY(5deg);
}

.counter-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.9));
    border-radius: 15px;
    padding: 30px 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 10px 25px rgba(30, 58, 95, 0.2);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.counter-card-inner::before {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    animation: counterShine 3s ease-in-out infinite;
    z-index: 0;
}

@keyframes counterShine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

.counter-number {
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    position: relative;
    z-index: 2;
}

.counter-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

/* Animated Border Cards for About Page */
.animated-border-box {
    position: relative;
    width: 100%;
    height: auto;
    min-height: 400px;
    background: #1a2332;
    border-radius: 15px;
    overflow: hidden;
    padding: 0;
}

.animated-border-box::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
}

.animated-border-box::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -3s;
}

.border-line {
    position: absolute;
    top: 0;
    inset: 0;
}

.border-line::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -1.5s;
}

.border-line::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 6s linear infinite;
    animation-delay: -4.5s;
}

@keyframes rotateBorder {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.animated-border-content {
    position: absolute;
    inset: 4px;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.95));
    padding: 40px 30px;
    border-radius: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: calc(100% - 8px);
    backdrop-filter: blur(10px);
}

/* Team member cards */
.team-card {
    position: relative;
    width: 100%;
    height: 350px;
    background: #1a2332;
    border-radius: 15px;
    overflow: hidden;
}

.team-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 8s linear infinite;
}

.team-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 8s linear infinite;
    animation-delay: -4s;
}

.team-content {
    position: absolute;
    inset: 3px;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.95));
    padding: 30px 20px;
    border-radius: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: calc(100% - 6px);
}

/* Counter cards */
.counter-card {
    position: relative;
    width: 100%;
    height: 200px;
    background: #1a2332;
    border-radius: 15px;
    overflow: hidden;
}

.counter-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffd700, #ffd700, #ffd700);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 5s linear infinite;
}

.counter-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, transparent, transparent, #ffed4e, #ffed4e, #ffed4e);
    z-index: 1;
    transform-origin: bottom right;
    animation: rotateBorder 5s linear infinite;
    animation-delay: -2.5s;
}

.counter-content {
    position: absolute;
    inset: 3px;
    background: linear-gradient(145deg, rgba(26, 35, 50, 0.95), rgba(15, 20, 25, 0.95));
    padding: 30px 20px;
    border-radius: 12px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: calc(100% - 6px);
}
</style>

            <!-- Dynamic Background -->
            <div class="about-page-bg"></div>

            <!-- Floating Particles -->
            <div class="floating-particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>

            <!-- Page Header -->
            <div class="cover-v1 jarallax" style="height: 60vh; min-height: 400px; position: relative; z-index: 1;">
                <div class="container">
                    <div class="row align-items-center h-100">
                        <div class="col-md-8 mx-auto text-center">
                            <h1 class="heading hero-title text-3d"><i class="icon-users heading-icon float"></i>About ISHTEEAHAR</h1>
                            <h2 class="subheading fade-in-up">Excellence in Digital Solutions Since Day One</h2>
                        </div>
                    </div>
                </div>
            </div>

            <!-- About Introduction -->
            <div class="unslate_co--section">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8 text-center">
                            <div class="box-3d p-5 fade-in-up">
                                <h2 class="heading-3d mb-4">Who We Are</h2>
                                <p class="lead">
                                    <?php echo getSetting('about_description', 'We are a full-service digital agency committed to delivering exceptional results for our clients. With expertise spanning web development, digital marketing, and creative services, we transform ideas into powerful digital experiences that drive business growth.'); ?>
                                </p>
                                <p>
                                    At ISHTEEAHAR, we believe in the power of digital transformation. Our team of passionate professionals
                                    combines creativity with technical expertise to deliver solutions that not only meet but exceed our clients' expectations.
                                    We're not just service providers; we're your partners in digital success.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mission, Vision, Values -->
            <div class="unslate_co--section" style="background: linear-gradient(135deg, #1a1a1a, #0d0d0d);">
                <div class="container">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="dynamic-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="0">
                                <div class="card-3d-wrapper">
                                    <div class="card-3d-inner">
                                        <div class="card-content">
                                            <i class="icon-target card-icon"></i>
                                            <h3 class="card-title">Our Mission</h3>
                                            <p class="card-text">
                                                To empower businesses with innovative digital solutions that drive growth,
                                                enhance user experiences, and create lasting value in the digital landscape.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="dynamic-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="100">
                                <div class="card-3d-wrapper">
                                    <div class="card-3d-inner">
                                        <div class="card-content">
                                            <i class="icon-eye card-icon"></i>
                                            <h3 class="card-title">Our Vision</h3>
                                            <p class="card-text">
                                                To be the leading digital agency that transforms businesses through
                                                cutting-edge technology, creative excellence, and strategic innovation.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-4">
                            <div class="dynamic-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="200">
                                <div class="card-3d-wrapper">
                                    <div class="card-3d-inner">
                                        <div class="card-content">
                                            <i class="icon-heart card-icon"></i>
                                            <h3 class="card-title">Our Values</h3>
                                            <p class="card-text">
                                                Excellence, integrity, innovation, and client success are at the core of everything we do.
                                                We believe in building lasting relationships through trust and exceptional service.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Our Story -->
            <div class="unslate_co--section">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal"><i class="icon-book-open heading-icon glow"></i>Our Story</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="box-3d p-5 fade-in-up">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h3 class="heading-3d mb-4">From Vision to Reality</h3>
                                        <p>
                                            ISHTEEAHAR was founded with a simple yet powerful vision: to bridge the gap between
                                            businesses and their digital potential. What started as a small team of passionate
                                            developers and designers has grown into a comprehensive digital agency.
                                        </p>
                                        <p>
                                            Our journey began when we recognized that many businesses were struggling to
                                            establish a strong digital presence. We saw an opportunity to combine our technical
                                            expertise with creative innovation to help these businesses thrive online.
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h3 class="heading-3d mb-4">Growing Together</h3>
                                        <p>
                                            Over the years, we've had the privilege of working with businesses of all sizes,
                                            from startups to established enterprises. Each project has taught us something new
                                            and helped us refine our approach to digital solutions.
                                        </p>
                                        <p>
                                            Today, ISHTEEAHAR stands as a testament to what's possible when passion meets
                                            purpose. We continue to evolve, embracing new technologies and methodologies
                                            to deliver even better results for our clients.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Section -->
            <div class="unslate_co--section" style="background: linear-gradient(135deg, #1a1a1a, #0d0d0d);">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal"><i class="icon-users heading-icon bounce"></i>Meet Our Team</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                        <p class="lead text-center mt-4 gsap-reveal">
                            Our diverse team of experts brings together years of experience in digital technologies,
                            creative design, and strategic marketing.
                        </p>
                    </div>

                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="team-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="0">
                                <div class="team-card-inner">
                                    <div class="team-photo">
                                        <div style="color: #000; font-size: 12px; font-weight: bold;">
                                            Team Photo
                                        </div>
                                    </div>
                                    <h4 class="team-name">John Smith</h4>
                                    <p class="team-role">Lead Developer</p>
                                    <p class="team-description">
                                        Expert in full-stack development with 8+ years of experience in creating
                                        scalable web applications and innovative digital solutions.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="team-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="100">
                                <div class="team-card-inner">
                                    <div class="team-photo">
                                        <div style="color: #000; font-size: 12px; font-weight: bold;">
                                            Team Photo
                                        </div>
                                    </div>
                                    <h4 class="team-name">Sarah Johnson</h4>
                                    <p class="team-role">Creative Director</p>
                                    <p class="team-description">
                                        Passionate designer with a keen eye for aesthetics and user experience.
                                        Specializes in brand identity and digital design solutions.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="team-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="200">
                                <div class="team-card-inner">
                                    <div class="team-photo">
                                        <div style="color: #000; font-size: 12px; font-weight: bold;">
                                            Team Photo
                                        </div>
                                    </div>
                                    <h4 class="team-name">Mike Chen</h4>
                                    <p class="team-role">Marketing Strategist</p>
                                    <p class="team-description">
                                        Digital marketing expert with proven track record in SEO, PPC, and
                                        social media marketing. Drives measurable results for clients.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-4">
                            <div class="team-3d-card fade-in-up" data-aos="fade-up" data-aos-delay="300">
                                <div class="team-card-inner">
                                    <div class="team-photo">
                                        <div style="color: #000; font-size: 12px; font-weight: bold;">
                                            Team Photo
                                        </div>
                                    </div>
                                    <h4 class="team-name">Emily Davis</h4>
                                    <p class="team-role">Project Manager</p>
                                    <p class="team-description">
                                        Experienced project manager ensuring smooth delivery and client satisfaction.
                                        Coordinates teams and manages timelines with precision.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="unslate_co--section section-counter">
                <div class="container">
                    <div class="section-heading-wrap text-center mb-5">
                        <h2 class="heading-h2 text-center divider heading-3d">
                            <span class="gsap-reveal"><i class="icon-trophy heading-icon rotate"></i>Our Achievements</span>
                        </h2>
                        <span class="gsap-reveal">
                            <img src="<?php echo IMAGES_URL; ?>/divider.png" alt="divider" width="76">
                        </span>
                    </div>

                    <div class="row pt-5">
                        <div class="col-6 col-sm-6 mb-5 mb-lg-0 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="0">
                            <div class="counter-3d-card">
                                <div class="counter-card-inner">
                                    <div class="counter-number">
                                        <span class="number-counter" data-number="150">0</span>
                                        <span>+</span>
                                    </div>
                                    <div class="counter-label">Projects Completed</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-sm-6 mb-5 mb-lg-0 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="100">
                            <div class="counter-3d-card">
                                <div class="counter-card-inner">
                                    <div class="counter-number">
                                        <span class="number-counter" data-number="98">0</span>
                                        <span>%</span>
                                    </div>
                                    <div class="counter-label">Client Satisfaction</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-sm-6 mb-5 mb-lg-0 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="200">
                            <div class="counter-3d-card">
                                <div class="counter-card-inner">
                                    <div class="counter-number">
                                        <span class="number-counter" data-number="5">0</span>
                                        <span>+</span>
                                    </div>
                                    <div class="counter-label">Years Experience</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6 col-sm-6 mb-5 mb-lg-0 col-md-6 col-lg-3" data-aos="fade-up" data-aos-delay="300">
                            <div class="counter-3d-card">
                                <div class="counter-card-inner">
                                    <div class="counter-number">
                                        <span class="number-counter" data-number="24">0</span>
                                        <span>/7</span>
                                    </div>
                                    <div class="counter-label">Support Available</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="unslate_co--section" style="background: linear-gradient(135deg, #1a1a1a, #0d0d0d);">
                <div class="container">
                    <div class="row">
                        <div class="col-md-8 mx-auto text-center">
                            <div class="box-3d p-5">
                                <h2 class="heading-3d mb-4">Ready to Work With Us?</h2>
                                <p class="lead mb-4">
                                    Join the growing list of satisfied clients who have transformed their
                                    digital presence with ISHTEEAHAR. Let's create something amazing together.
                                </p>
                                <a href="contact.php" class="btn btn-custom-3d btn-lg mr-3">Get Started Today</a>
                                <a href="services.php" class="btn btn-outline-pill btn-custom-light btn-lg">Explore Services</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<?php
// Include footer
include 'includes/footer.php';
?>
