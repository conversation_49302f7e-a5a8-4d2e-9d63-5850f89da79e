<?php
/**
 * Database Configuration for ISHTEEAHAR Digital Agency
 * 
 * This file contains database connection settings and PDO connection setup
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'ishteeahar_agency');
define('DB_USER', 'root');  // Change this to your MySQL username
define('DB_PASS', '');      // Change this to your MySQL password
define('DB_CHARSET', 'utf8mb4');

// PDO Options
$pdo_options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
];

// Global PDO connection variable
$pdo = null;

/**
 * Get database connection
 * 
 * @return PDO Database connection object
 * @throws PDOException If connection fails
 */
function getDbConnection() {
    global $pdo, $pdo_options;
    
    if ($pdo === null) {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $pdo = new PDO($dsn, DB_USER, DB_PASS, $pdo_options);
        } catch (PDOException $e) {
            // Log error in production, display in development
            error_log("Database connection failed: " . $e->getMessage());
            throw new PDOException("Database connection failed. Please try again later.");
        }
    }
    
    return $pdo;
}

/**
 * Execute a prepared statement with parameters
 * 
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters for the query
 * @return PDOStatement Executed statement
 */
function executeQuery($sql, $params = []) {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt;
}

/**
 * Fetch single row from database
 * 
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return array|false Single row or false if not found
 */
function fetchSingle($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Fetch multiple rows from database
 * 
 * @param string $sql SQL query
 * @param array $params Query parameters
 * @return array Array of rows
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert data into database
 * 
 * @param string $table Table name
 * @param array $data Associative array of column => value
 * @return int Last insert ID
 */
function insertData($table, $data) {
    $columns = implode(', ', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));
    
    $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    $stmt = executeQuery($sql, $data);
    
    $pdo = getDbConnection();
    return $pdo->lastInsertId();
}

/**
 * Update data in database
 * 
 * @param string $table Table name
 * @param array $data Associative array of column => value
 * @param string $where WHERE clause
 * @param array $whereParams Parameters for WHERE clause
 * @return int Number of affected rows
 */
function updateData($table, $data, $where, $whereParams = []) {
    $setClause = [];
    foreach (array_keys($data) as $column) {
        $setClause[] = "{$column} = :{$column}";
    }
    $setClause = implode(', ', $setClause);
    
    $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
    $params = array_merge($data, $whereParams);
    $stmt = executeQuery($sql, $params);
    
    return $stmt->rowCount();
}

/**
 * Delete data from database
 * 
 * @param string $table Table name
 * @param string $where WHERE clause
 * @param array $params Parameters for WHERE clause
 * @return int Number of affected rows
 */
function deleteData($table, $where, $params = []) {
    $sql = "DELETE FROM {$table} WHERE {$where}";
    $stmt = executeQuery($sql, $params);
    return $stmt->rowCount();
}

/**
 * Check if database connection is working
 * 
 * @return bool True if connection is working
 */
function testDbConnection() {
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->query("SELECT 1");
        return $stmt !== false;
    } catch (Exception $e) {
        return false;
    }
}

// Test connection on include (only in development)
if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
    if (!testDbConnection()) {
        die("Database connection failed. Please check your configuration.");
    }
}
?>
