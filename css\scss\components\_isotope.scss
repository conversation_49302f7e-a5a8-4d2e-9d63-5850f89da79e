
.grid-item {
  position: relative;
}

.grid-item:before {
  content: '';
  display: inline-block;
  padding-top: 20rem;
}


.grid-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}


.blog-item {
  position: relative;
  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    object-fit: cover;
  }
}
.portfolio-item {
  overflow: hidden;
  img {
    transition: .3s all ease;
    position: relative;
    transform: scale(1.0);
  }
  &:hover {
    img {
      transform: scale(1.09);
    }
  }
}
.portfolio-item, .blog-item {
  display: block;
  overflow: hidden;
  position: relative;
  .overlay {
    position: relative;
    z-index: 8;
    opacity: 0;
    visibility: hidden;
    transition: .3s all ease;
    &, &:before {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
    &:before {
      background: rgba($black, .5);
      content: "";
    }
  }
  .wrap-icon {
    position: absolute;
    right: 20px;
    color: $white;
    top: 20px;
  }
  .portfolio-item-content {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    h3,p {
      color: $white;
      margin: 0;
      padding: 0;
    }
    h3 {
      font-size: 18px;
    }
    p {
      font-size: 12px;
      color: rgba($white, .5);
      font-family: $font-family-sans-serif-secondary;
    }
  }
  &:hover {
    .overlay {
      opacity: 1;
      visibility: visible;
    }
  }
}
.blog-item {
  margin-left: 0!important;
  .overlay {
    opacity: 1;
    visibility: visible;
  }
  &:hover {
    .overlay {
      opacity: 0;
      visibility: hidden;
    } 
  }
}

.filter-wrap {
  position: relative;
  
  .filter {
    font-size: 14px;
    color: $white;
    
    
    a {
      margin-left: 10px;
      color: rgba($white, .5);
      &:hover {
        color: $white;
      }
      &.active {
        color: $white;
      }
    }

    
    @include media-breakpoint-down(md) {
      background: $black;
      z-index: 9;
      position: absolute;
      min-width: 150px;
      opacity: 0;
      visibility: hidden;
      transition: .3s all ease;
      right: 0;
      padding: 20px;
      top: 10px;
      box-shadow: 0 2px 5px 0 rgba($black, .2);
      border-radius: 4px;
      &.active {
        opacity: 1;
        visibility: visible;
      }
      a {
        display: block;
      }
    }
  }
}


/* Isotope Transitions
------------------------------- */
.isotope,
.isotope .item {
  -webkit-transition-duration: 0.8s;
     -moz-transition-duration: 0.8s;
      -ms-transition-duration: 0.8s;
       -o-transition-duration: 0.8s;
          transition-duration: 0.8s;
}

.isotope {
  -webkit-transition-property: height, width;
     -moz-transition-property: height, width;
      -ms-transition-property: height, width;
       -o-transition-property: height, width;
          transition-property: height, width;
}

.isotope .item {
  -webkit-transition-property: -webkit-transform, opacity;
     -moz-transition-property:    -moz-transform, opacity;
      -ms-transition-property:     -ms-transform, opacity;
       -o-transition-property:         top, left, opacity;
          transition-property:         transform, opacity;
}