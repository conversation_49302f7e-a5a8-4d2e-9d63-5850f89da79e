{"name": "unbrand", "firstRun": false, "exportConfig": true, "fileConfigs": [{"path": "css/scss/bootstrap/bootstrap.scss", "configJson": "{\"forceCompile\":false,\"customOutput\":\"css/vendor/bootstrap.min.css\",\"autoCompile\":true,\"sourceMap\":true,\"compiler-node-sass\":{\"enabled\":true,\"outputStyle\":\"compact\"},\"compiler-autoprefixer\":{\"enabled\":true},\"compiler-minify-css\":{\"enabled\":true}}"}, {"path": "css/scss/style.scss", "configJson": "{\"forceCompile\":false,\"customOutput\":\"css/style.css\",\"autoCompile\":true,\"sourceMap\":false,\"compiler-node-sass\":{\"enabled\":true,\"outputStyle\":\"expanded\"},\"compiler-autoprefixer\":{\"enabled\":true},\"compiler-minify-css\":{\"enabled\":false}}"}, {"path": "css/scss/style-light.scss", "configJson": "{\"forceCompile\":false,\"customOutput\":\"css/style-light.css\",\"autoCompile\":true,\"sourceMap\":false,\"compiler-node-sass\":{\"enabled\":true,\"outputStyle\":\"expanded\"},\"compiler-autoprefixer\":{\"enabled\":true},\"compiler-minify-css\":{\"enabled\":false}}"}], "fileTree": {"expandedDirs": ["css", "css/scss", "css/scss/bootstrap"], "hideSystemFiles": true, "systemFiles": [".*", "desktop.ini", "prepros.config", "$RECYCLE.BIN", "prepros.cfg", "prepros-6.config", "Prepros Export"], "hideUnwatchedFiles": false}, "imports": [{"path": "css/scss/style.scss", "imports": ["css/scss/bootstrap/_functions.scss", "css/scss/bootstrap/_variables.scss", "css/scss/bootstrap/_mixins.scss", "css/scss/components/_base.scss", "css/scss/components/_main-nav.scss", "css/scss/components/_cover.scss", "css/scss/components/_isotope.scss", "css/scss/components/_contents.scss", "css/scss/bootstrap/vendor/_rfs.scss", "css/scss/bootstrap/mixins/_deprecate.scss", "css/scss/bootstrap/mixins/_breakpoints.scss", "css/scss/bootstrap/mixins/_hover.scss", "css/scss/bootstrap/mixins/_image.scss", "css/scss/bootstrap/mixins/_badge.scss", "css/scss/bootstrap/mixins/_resize.scss", "css/scss/bootstrap/mixins/_screen-reader.scss", "css/scss/bootstrap/mixins/_size.scss", "css/scss/bootstrap/mixins/_reset-text.scss", "css/scss/bootstrap/mixins/_text-emphasis.scss", "css/scss/bootstrap/mixins/_text-hide.scss", "css/scss/bootstrap/mixins/_text-truncate.scss", "css/scss/bootstrap/mixins/_visibility.scss", "css/scss/bootstrap/mixins/_alert.scss", "css/scss/bootstrap/mixins/_buttons.scss", "css/scss/bootstrap/mixins/_caret.scss", "css/scss/bootstrap/mixins/_pagination.scss", "css/scss/bootstrap/mixins/_lists.scss", "css/scss/bootstrap/mixins/_list-group.scss", "css/scss/bootstrap/mixins/_nav-divider.scss", "css/scss/bootstrap/mixins/_forms.scss", "css/scss/bootstrap/mixins/_table-row.scss", "css/scss/bootstrap/mixins/_background-variant.scss", "css/scss/bootstrap/mixins/_border-radius.scss", "css/scss/bootstrap/mixins/_box-shadow.scss", "css/scss/bootstrap/mixins/_gradients.scss", "css/scss/bootstrap/mixins/_transition.scss", "css/scss/bootstrap/mixins/_clearfix.scss", "css/scss/bootstrap/mixins/_grid-framework.scss", "css/scss/bootstrap/mixins/_grid.scss", "css/scss/bootstrap/mixins/_float.scss"]}, {"path": "css/scss/bootstrap/_mixins.scss", "imports": ["css/scss/bootstrap/vendor/_rfs.scss", "css/scss/bootstrap/mixins/_deprecate.scss", "css/scss/bootstrap/mixins/_breakpoints.scss", "css/scss/bootstrap/mixins/_hover.scss", "css/scss/bootstrap/mixins/_image.scss", "css/scss/bootstrap/mixins/_badge.scss", "css/scss/bootstrap/mixins/_resize.scss", "css/scss/bootstrap/mixins/_screen-reader.scss", "css/scss/bootstrap/mixins/_size.scss", "css/scss/bootstrap/mixins/_reset-text.scss", "css/scss/bootstrap/mixins/_text-emphasis.scss", "css/scss/bootstrap/mixins/_text-hide.scss", "css/scss/bootstrap/mixins/_text-truncate.scss", "css/scss/bootstrap/mixins/_visibility.scss", "css/scss/bootstrap/mixins/_alert.scss", "css/scss/bootstrap/mixins/_buttons.scss", "css/scss/bootstrap/mixins/_caret.scss", "css/scss/bootstrap/mixins/_pagination.scss", "css/scss/bootstrap/mixins/_lists.scss", "css/scss/bootstrap/mixins/_list-group.scss", "css/scss/bootstrap/mixins/_nav-divider.scss", "css/scss/bootstrap/mixins/_forms.scss", "css/scss/bootstrap/mixins/_table-row.scss", "css/scss/bootstrap/mixins/_background-variant.scss", "css/scss/bootstrap/mixins/_border-radius.scss", "css/scss/bootstrap/mixins/_box-shadow.scss", "css/scss/bootstrap/mixins/_gradients.scss", "css/scss/bootstrap/mixins/_transition.scss", "css/scss/bootstrap/mixins/_clearfix.scss", "css/scss/bootstrap/mixins/_grid-framework.scss", "css/scss/bootstrap/mixins/_grid.scss", "css/scss/bootstrap/mixins/_float.scss"]}, {"path": "css/scss/bootstrap/_utilities.scss", "imports": ["css/scss/bootstrap/utilities/_align.scss", "css/scss/bootstrap/utilities/_background.scss", "css/scss/bootstrap/utilities/_borders.scss", "css/scss/bootstrap/utilities/_clearfix.scss", "css/scss/bootstrap/utilities/_display.scss", "css/scss/bootstrap/utilities/_embed.scss", "css/scss/bootstrap/utilities/_flex.scss", "css/scss/bootstrap/utilities/_float.scss", "css/scss/bootstrap/utilities/_overflow.scss", "css/scss/bootstrap/utilities/_position.scss", "css/scss/bootstrap/utilities/_screenreaders.scss", "css/scss/bootstrap/utilities/_shadows.scss", "css/scss/bootstrap/utilities/_sizing.scss", "css/scss/bootstrap/utilities/_stretched-link.scss", "css/scss/bootstrap/utilities/_spacing.scss", "css/scss/bootstrap/utilities/_text.scss", "css/scss/bootstrap/utilities/_visibility.scss"]}, {"path": "css/scss/bootstrap/bootstrap-grid.scss", "imports": ["css/scss/bootstrap/_functions.scss", "css/scss/bootstrap/_variables.scss", "css/scss/bootstrap/mixins/_breakpoints.scss", "css/scss/bootstrap/mixins/_grid-framework.scss", "css/scss/bootstrap/mixins/_grid.scss", "css/scss/bootstrap/_grid.scss", "css/scss/bootstrap/utilities/_display.scss", "css/scss/bootstrap/utilities/_flex.scss", "css/scss/bootstrap/utilities/_spacing.scss"]}, {"path": "css/scss/bootstrap/bootstrap-reboot.scss", "imports": ["css/scss/bootstrap/_functions.scss", "css/scss/bootstrap/_variables.scss", "css/scss/bootstrap/_mixins.scss", "css/scss/bootstrap/_reboot.scss", "css/scss/bootstrap/vendor/_rfs.scss", "css/scss/bootstrap/mixins/_deprecate.scss", "css/scss/bootstrap/mixins/_breakpoints.scss", "css/scss/bootstrap/mixins/_hover.scss", "css/scss/bootstrap/mixins/_image.scss", "css/scss/bootstrap/mixins/_badge.scss", "css/scss/bootstrap/mixins/_resize.scss", "css/scss/bootstrap/mixins/_screen-reader.scss", "css/scss/bootstrap/mixins/_size.scss", "css/scss/bootstrap/mixins/_reset-text.scss", "css/scss/bootstrap/mixins/_text-emphasis.scss", "css/scss/bootstrap/mixins/_text-hide.scss", "css/scss/bootstrap/mixins/_text-truncate.scss", "css/scss/bootstrap/mixins/_visibility.scss", "css/scss/bootstrap/mixins/_alert.scss", "css/scss/bootstrap/mixins/_buttons.scss", "css/scss/bootstrap/mixins/_caret.scss", "css/scss/bootstrap/mixins/_pagination.scss", "css/scss/bootstrap/mixins/_lists.scss", "css/scss/bootstrap/mixins/_list-group.scss", "css/scss/bootstrap/mixins/_nav-divider.scss", "css/scss/bootstrap/mixins/_forms.scss", "css/scss/bootstrap/mixins/_table-row.scss", "css/scss/bootstrap/mixins/_background-variant.scss", "css/scss/bootstrap/mixins/_border-radius.scss", "css/scss/bootstrap/mixins/_box-shadow.scss", "css/scss/bootstrap/mixins/_gradients.scss", "css/scss/bootstrap/mixins/_transition.scss", "css/scss/bootstrap/mixins/_clearfix.scss", "css/scss/bootstrap/mixins/_grid-framework.scss", "css/scss/bootstrap/mixins/_grid.scss", "css/scss/bootstrap/mixins/_float.scss"]}, {"path": "css/scss/bootstrap/bootstrap.scss", "imports": ["css/scss/bootstrap/_functions.scss", "css/scss/bootstrap/_variables.scss", "css/scss/bootstrap/_mixins.scss", "css/scss/bootstrap/_root.scss", "css/scss/bootstrap/_reboot.scss", "css/scss/bootstrap/_type.scss", "css/scss/bootstrap/_images.scss", "css/scss/bootstrap/_code.scss", "css/scss/bootstrap/_grid.scss", "css/scss/bootstrap/_tables.scss", "css/scss/bootstrap/_forms.scss", "css/scss/bootstrap/_buttons.scss", "css/scss/bootstrap/_transitions.scss", "css/scss/bootstrap/_dropdown.scss", "css/scss/bootstrap/_button-group.scss", "css/scss/bootstrap/_input-group.scss", "css/scss/bootstrap/_custom-forms.scss", "css/scss/bootstrap/_nav.scss", "css/scss/bootstrap/_navbar.scss", "css/scss/bootstrap/_card.scss", "css/scss/bootstrap/_breadcrumb.scss", "css/scss/bootstrap/_pagination.scss", "css/scss/bootstrap/_badge.scss", "css/scss/bootstrap/_jumbotron.scss", "css/scss/bootstrap/_alert.scss", "css/scss/bootstrap/_progress.scss", "css/scss/bootstrap/_media.scss", "css/scss/bootstrap/_list-group.scss", "css/scss/bootstrap/_close.scss", "css/scss/bootstrap/_toasts.scss", "css/scss/bootstrap/_modal.scss", "css/scss/bootstrap/_tooltip.scss", "css/scss/bootstrap/_popover.scss", "css/scss/bootstrap/_carousel.scss", "css/scss/bootstrap/_spinners.scss", "css/scss/bootstrap/_utilities.scss", "css/scss/bootstrap/_print.scss", "css/scss/bootstrap/vendor/_rfs.scss", "css/scss/bootstrap/mixins/_deprecate.scss", "css/scss/bootstrap/mixins/_breakpoints.scss", "css/scss/bootstrap/mixins/_hover.scss", "css/scss/bootstrap/mixins/_image.scss", "css/scss/bootstrap/mixins/_badge.scss", "css/scss/bootstrap/mixins/_resize.scss", "css/scss/bootstrap/mixins/_screen-reader.scss", "css/scss/bootstrap/mixins/_size.scss", "css/scss/bootstrap/mixins/_reset-text.scss", "css/scss/bootstrap/mixins/_text-emphasis.scss", "css/scss/bootstrap/mixins/_text-hide.scss", "css/scss/bootstrap/mixins/_text-truncate.scss", "css/scss/bootstrap/mixins/_visibility.scss", "css/scss/bootstrap/mixins/_alert.scss", "css/scss/bootstrap/mixins/_buttons.scss", "css/scss/bootstrap/mixins/_caret.scss", "css/scss/bootstrap/mixins/_pagination.scss", "css/scss/bootstrap/mixins/_lists.scss", "css/scss/bootstrap/mixins/_list-group.scss", "css/scss/bootstrap/mixins/_nav-divider.scss", "css/scss/bootstrap/mixins/_forms.scss", "css/scss/bootstrap/mixins/_table-row.scss", "css/scss/bootstrap/mixins/_background-variant.scss", "css/scss/bootstrap/mixins/_border-radius.scss", "css/scss/bootstrap/mixins/_box-shadow.scss", "css/scss/bootstrap/mixins/_gradients.scss", "css/scss/bootstrap/mixins/_transition.scss", "css/scss/bootstrap/mixins/_clearfix.scss", "css/scss/bootstrap/mixins/_grid-framework.scss", "css/scss/bootstrap/mixins/_grid.scss", "css/scss/bootstrap/mixins/_float.scss", "css/scss/bootstrap/utilities/_align.scss", "css/scss/bootstrap/utilities/_background.scss", "css/scss/bootstrap/utilities/_borders.scss", "css/scss/bootstrap/utilities/_clearfix.scss", "css/scss/bootstrap/utilities/_display.scss", "css/scss/bootstrap/utilities/_embed.scss", "css/scss/bootstrap/utilities/_flex.scss", "css/scss/bootstrap/utilities/_float.scss", "css/scss/bootstrap/utilities/_overflow.scss", "css/scss/bootstrap/utilities/_position.scss", "css/scss/bootstrap/utilities/_screenreaders.scss", "css/scss/bootstrap/utilities/_shadows.scss", "css/scss/bootstrap/utilities/_sizing.scss", "css/scss/bootstrap/utilities/_stretched-link.scss", "css/scss/bootstrap/utilities/_spacing.scss", "css/scss/bootstrap/utilities/_text.scss", "css/scss/bootstrap/utilities/_visibility.scss"]}], "projectView": {"selectedView": "file-tree"}, "fileWatcher": {"enabled": true, "watchedExtensions": ["less", "sass", "scss", "styl", "md", "markdown", "coffee", "js", "jade", "haml", "slim", "ls", "kit", "png", "jpg", "jpeg", "ts", "pug", "css", "html", "htm", "php"]}, "pathFilters": ["node_modules", ".*", "bower_components", "prepros.config", "Prepros Export", "prepros-6.config", "prepros.cfg", "wp-admin", "wp-includes"], "server": {"port": 7989, "assignNewPortAutomatically": true, "enable": true, "proxy": {"enable": false, "url": ""}}, "browser-sync": {"enable": false, "clicks": true, "forms": true, "scroll": true}, "live-reload": {"enable": true, "animate": true, "delay": 0}, "ftp-deploy": {"connectionType": "ftp", "remotePath": "", "uploadTimeout": 20000, "uploadOnChange": false, "ftp": {"secure": false, "keepAlive": true, "host": "", "port": 21, "user": "", "password": ""}, "sftp": {"host": "", "port": 22, "usePrivateKey": false, "username": "", "password": "", "privateKey": "", "passphrase": ""}, "pathFilters": ["config.rb", "prepros.config", "prepros-6.config", "node_modules", "Prepros Export", ".git", ".idea", ".sass-cache", ".hg", ".svn", ".cache", ".DS_Store", "*.sass", "*.scss", "*.less", "*.pug", "*.jade", "*.styl", "*.haml", "*.slim", "*.coffee", "*.ls", "*.kit", "*.ts"], "history": []}, "file-type-sass": "{\"compilers\":[\"node-sass\",\"autoprefixer\",\"minify-css\"]}", "file-type-less": "{\"compilers\":[\"less\",\"autoprefixer\",\"minify-css\"]}", "autoprefixer": {"browsers": "last 5 versions"}, "file-type-pug": "{\"compilers\":[\"pug\"]}", "file-type-css": "{\"compilers\":[\"autoprefixer\",\"cssnext\",\"minify-css\"]}", "file-type-javascript": "{\"compilers\":[\"concat-js\",\"babel\",\"uglify-js\"]}", "file-type-stylus": "{\"compilers\":[\"stylus\",\"autoprefixer\",\"minify-css\"]}", "file-type-markdown": "{\"compilers\":[\"markdown\"]}", "file-type-haml": "{\"compilers\":[\"haml\"]}", "file-type-slim": "{\"compilers\":[\"slim\"]}", "file-type-coffee-script": "{\"compilers\":[\"coffee-script\",\"uglify-js\"]}", "file-type-livescript": "{\"compilers\":[\"livescript\",\"uglify-js\"]}", "file-type-kit": "{\"compilers\":[\"kit\"]}", "uglify-js": {"ie8": false, "compress": {"sequences": true, "properties": true, "dead_code": true, "drop_debugger": true, "unsafe": false, "unsafe_comps": false, "unsafe_math": false, "unsafe_proto": false, "unsafe_regexp": false, "conditionals": true, "comparisons": true, "evaluate": true, "booleans": true, "loops": true, "unused": true, "toplevel": false, "top_retain": "", "hoist_funs": true, "hoist_vars": false, "if_return": true, "join_vars": true, "collapse_vars": true, "reduce_vars": true, "warnings": true, "negate_iife": true, "pure_getters": false, "pure_funcs": [], "drop_console": false, "expression": false, "keep_fargs": true, "keep_fnames": false, "passes": 1, "keep_infinity": false, "side_effects": true, "global_defs": []}, "output": {"ascii_only": false, "beautify": false, "comments": "", "indent_level": 4, "indent_start": 0, "inline_script": false, "keep_quoted_props": false, "max_line_len": false, "preamble": "", "preserve_line": false, "quote_keys": false, "quote_style": 0, "semicolons": true, "shebang": true, "width": 80}}, "cssnext": {"customProperties": true, "applyRule": true, "calc": false, "nesting": true, "customMedia": true, "mediaQueriesRange": true, "customSelectors": true, "attributeCaseInsensitive": true, "colorRebeccapurple": true, "colorHwb": true, "colorGray": true, "colorHexAlpha": true, "colorFunction": true, "fontVariant": true, "filter": true, "initial": true, "rem": true, "pseudoElements": true, "pseudoClassMatches": true, "pseudoClassNot": true, "pseudoClassAnyLink": true, "colorRgba": true, "overflowWrap": true}, "file-type-typescript": "{\"compilers\":[\"typescript\",\"uglify-js\"]}", "babel": {"useBabelRc": true, "presets": {"babel-preset-es2015": true}, "plugins": {"babel-plugin-syntax-jsx": true, "babel-plugin-transform-react-jsx": true, "babel-plugin-transform-async-to-generator": true, "babel-plugin-transform-class-properties": true, "babel-plugin-transform-object-rest-spread": true}}, "file-type-png": "{\"compilers\":[\"png\"]}", "file-type-jpg": "{\"compilers\":[\"jpg\"],\"compiler-jpg\":{\"enabled\":true,\"originalSize\":0,\"newSize\":0}}"}