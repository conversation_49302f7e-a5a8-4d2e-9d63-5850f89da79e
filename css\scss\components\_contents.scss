.unslate_co--section {
	padding: 3.5rem 0;
	@include media-breakpoint-down(sm) {
		padding: 2rem 0;
	}
	a {
		&:hover {
			color: $white;
		}
	}
}
.heading-h2 {
	font-size: 32px;
	color: $white;
	font-weight: 900;
	&.divider {
		&:before {
			position: absolute;
			content: "";
		}
	}
}
.heading-h3 {
	font-size: 26px;
	font-weight: 900;
	color: $white;
}
.lead {
	font-size: 18px;
	line-height: 30px
}

.section-heading-wrap {

}
.relative {
	position: relative;
}

figure{
	&.dotted-bg {
		position: relative;
		&:before {
			z-index: -1;
			content: "";
			position: absolute;
			bottom: -50px;
			right: -50px;
			width: 268px;
			height: 224px;
			background-image: url('../images/dotted_light.png');
			background-repeat: no-repeat;
			@include media-breakpoint-down(md) {
				right: 0px;
			}
		}
	}
}



// custom gutter
.gutter-v1 {
    margin-right: -1px!important;
    margin-left: -1px!important;
    @include media-breakpoint-down(md) {
	  	margin-left: -15px!important;
	  	margin-right: -15px!important;
	  }
  > .col,
  > [class*="col-"] {
    padding-right: 1px!important;
    padding-left: 1px!important;
    @include media-breakpoint-down(md) {
    	padding-right: 15px!important;
    	padding-left: 15px!important;
    }
  }
}
.gutter-v2 {
    margin-right: -2px!important;
    margin-left: -2px!important;
    @include media-breakpoint-down(md) {
	  	margin-left: -15px!important;
	  	margin-right: -15px!important;
	  }
  > .col,
  > [class*="col-"] {
  	margin-bottom: 4px;
    padding-right: 2px!important;
    padding-left: 2px!important;
    @include media-breakpoint-down(md) {
    	padding-right: 15px!important;
    	padding-left: 15px!important;
    }
  }
}
.gutter-v3 {
  margin-left: -60px!important;
  margin-right: -60px!important;
  @include media-breakpoint-down(md) {
  	margin-left: -15px!important;
  	margin-right: -15px!important;
  }
  > .col,
  > [class*="col-"] {
    padding-right: 60px!important;
    padding-left: 60px!important;
    @include media-breakpoint-down(md) {
    	padding-right: 15px!important;
    	padding-left: 15px!important;
    }
  }
}
.gutter-v4 {
    margin-right: -10px!important;
    margin-left: -10px!important;
  > .col,
  > [class*="col-"] {
  	margin-bottom: 20px;
    padding-right: 10px!important;
    padding-left: 10px!important;
  }
}

.gutter-isotope-item {
    margin-right: -15px!important;
    margin-left: -15px!important;
    @include media-breakpoint-down(md) {
	  	margin-left: -15px!important;
	  	margin-right: -15px!important;
	  }
  > .col,
  > [class*="col-"] {
    padding-right: 15px!important;
    padding-left: 15px!important;
    
    @include media-breakpoint-down(md) {
    	padding-right: 15px!important;
    	padding-left: 15px!important;
    }
  }

}
.isotope-mb-2 {
	margin-bottom: 30px!important;
}

.feature-v1 {
	line-height: 28px;
	@include media-breakpoint-down(sm) {
		text-align: center;
	}
	h3 {
		font-size: 22px;
		font-weight: 900;
	}
	p {
		font-size: 16px;	
	}
}


.counter-v1 {
	.number-wrap {
		display: block;
		.number {
			font-size: 69px;
			font-weight: 900;
			font-family: $font-family-sans-serif-secondary;
			color: $primary;
		}
		.append-text {
			position: relative;
			top: -30px;
			font-size: 28px;
			font-weight: 900;

		}
	}
	.counter-label {
		text-transform: uppercase;
		letter-spacing: .1rem;
		font-size: 11px;
		font-weight: 900;
	}
}


.testimonial-v1 {
	text-align: center;
	z-index: 9;
	.testimonial-inner-bg {
		padding: 50px;
		padding-bottom: 70px;
		background-color: #212121;
		text-align: center;
		font-size: 20px;
		line-height: 40px;
		position: relative;
		z-index: 1;
		box-shadow: 0px 30px 0 -20px rgba($primary, 1);
		@include media-breakpoint-down(md) {
			font-size: 16px;
			line-height: 32px;
			padding: 30px 30px 50px 30px;
		}
		.quote {
			display: block;
			font-size: 80px;
			font-family: 'Georgia', serif;
		}
	}

	.testimonial-author-info {

		z-index: 2;
		margin-top: -45px;
		position: relative;
		img {
			width: 90px;
			margin: 0 auto 30px auto;
			border-radius: 50%;
		}
		h3 {
			font-size: 20px;
			margin: 0;
		}
		.position {
			font-size: 12px;
			color: rgba($white, .5);
		}
		
	}
}

// owl slider custom
.testimonial-slider, .single-slider {
	// position: relative;
	.owl-stage {
		// position: relative;
		overflow: hidden;
	}
	.owl-nav {
    @include media-breakpoint-down(md) {
      display: none;
    }
    .owl-prev,
    .owl-next {
      position: absolute;
      bottom: -70px;
      z-index: 99;
      transform: translateY(-50%);
      color: $white!important;
      span {
      	font-size: 30px;
      }
      &:hover {
        color: $white;
      }
      &:active, &:focus {
      	outline: none;
      }
    }
    .owl-prev {
      left: 60px;
    }
    .owl-next {
      right: 60px;
    }
  }
  .owl-dots {
  	position: absolute;
  	width: 100%;
  	text-align: center;
  	bottom: 50px;
  	.owl-dot {
  		display: inline-block;
  		> span {
  			margin: 4px;
  			display: inline-block;
	  		width: 8px;
	  		height: 8px;
	  		border-radius: 50%;
	  		background: rgba($white, .5);
	  		transition: .3s all ease;
  		}
  		&.active {
				> span {
					background: rgba($white, 1);
				}
  		}
  		&:active, &:focus {
  			outline: none;
  		}
  	}
  }
}
.testimonial-slider {
	.owl-dots {
		bottom: -50px;
	}
}

.single-slider {
	margin-bottom: 90px;
	.owl-prev, .owl-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 99;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba($black, .2)!important;
    transition: .3s all ease;
		&:hover {
			background: rgba($black, .9)!important;
		}
    span {
    	position: absolute;
    	top: 50%;
    	left: 50%;
    	transform: translate(-50%, -50%);
	
    	&:before {
    		font-size: 40px;
    	}
    }
  }
 
}

// Blog post
.blog-post-entry {
	margin-bottom: 30px;
	.post-meta {
		font-family: $font-family-sans-serif-secondary;
	}
}

// form
.form-outline-style-v1 {
	
	.form-group	{
		position: relative;
		margin-bottom: 50px;
		label {
			position: absolute;
			top: 5px;
			transition: .3s all ease;
		}
		.form-control {
			border: none;
			background: none;
			padding-left: 0;
			padding-right: 0;
			border-radius: 0;
			color: $white;
			border-bottom: 1px solid rgba($white, .5);
			&:active, &:focus {
				outline: none;
				box-shadow: none;
				border-bottom: 1px solid rgba($white, 1);
			}
		}
		label {
			font-size: 12px;
			display: block;
			margin-bottom: 0;
			color: darken(#ccc, 10%);
			text-transform: uppercase;
			font-weight: 900;
		}
		&.focus {
			background: $white;
		}
		&.field--not-empty {
			label {
				margin-top: -25px;
			}
		}
	}
}

.form-control {
			border: none;
			background: none;
			padding-left: 0;
			padding-right: 0;
			border-radius: 0;
			color: $white;
			border-bottom: 1px solid rgba($white, .5);
			&:active, &:focus {
				background: none;
				outline: none;
				box-shadow: none;
				border-bottom: 1px solid rgba($white, 1);
			}
		}


.contact-info-v1 {
	@include media-breakpoint-down(sm) {
		text-align: center;
	}
	> div {
		margin-bottom: 30px;
	}
	.contact-info-label {
		font-size: 11px;
		letter-spacing: .1rem;
		font-weight: 900;
		text-transform: uppercase;
		color: rgba($white, .5);
	}
	.contact-info-val {
		font-size: 20px;
		color: $white;
	}
}


.portfolio-single-inner {
	.heading-portfolio-single-h2 {
		margin-bottom: 40px;
		font-size: 50px;
		font-weight: 900;
		@include media-breakpoint-down(md) {
			font-size: 30px;
		}
	}
}



#portfolio-single-holder {
	.portfolio-single-inner {
		position: relative;
		opacity: 0;
		display: none;
	}
}

.unslate_co--close-portfolio {
	position: absolute;
	top: -10px;
	cursor: pointer;
	z-index: 9;
	padding: 10px 10px 10px 10px;
	right: -10px;
	border: 2px solid transparent;
	background-color: #212121;
	border-radius: 30px;
	transition: .3s all ease;
	.close-portfolio-label {
		font-size: 14px;
		top: 25px;
		right: -10px;
		width: 150px;
		// display: none;
		position: absolute;
		transform: translateY(-50%);
		opacity: 0;
		visibility: hidden;
		transition: .3s all ease;
	}
	.wrap-icon-close {
		font-size: 30px;
		position: relative;
		transition: .3s all ease;
	}
	&:hover {
		.wrap-icon-close {
			transform: rotate(90deg);
		}
		.close-portfolio-lanel {
			opacity: 1;
			left: -10px;
			visibility: visible;
		}	
	}
	&:hover, &:focus {
		border-color: lighten(#212121, 7%);
		background-color: lighten(#212121, 5%);
		.close-portfolio-label {
			right: 20px;
			opacity: 1;
			visibility: visible;
		}
	}
}

.close-portfolio--content {
	right: 50%;
	transform: translateX(50%);
	.close-portfolio-label {
		right: 50%!important;
		transform: translateX(50%);
		top: 50px;
	}
}


.detail-v1 {
	.detail-label {
		display: block;
		font-size: 11px;
		font-weight: 900;
		text-transform: uppercase;
		letter-spacing: .1rem;
		color: rgba($white, .5);
	}	
	.detail-val {
		font-size: 16px;
		color: $white;
		a {
			color: $white;
		}
	}
}


.unslate_co--footer {
	text-align: center;
	font-weight: 300;
	color: rgba($white, .5);
	a {
		color: rgba($white, .6);
		&:hover {
			color: $white;
		}
	}

	.footer-site-logo {
		margin-bottom: 30px;
		a {
			font-size: 30px;
			font-weight: 900;
			font-family: $font-family-sans-serif;
			color: $white;
			span {
				color: $primary;
			}
		}
	}
	.site-copyright {
	}
	.footer-site-social {
		padding: 0;
		margin: 0;
		margin-bottom: 30px;
		li {
			padding: 0;
			margin: 0;
			list-style: none;
			display: inline-block;
			@include media-breakpoint-down(md) {
				display: block;
			}
			a {
				display: inline-block;
				padding-left: 20px;
				padding-right: 20px;
				color: rgba($white, .5);
				letter-spacing: .1rem;
				text-transform: uppercase;
				font-size: 12px;
				font-weight: bold;
				&:hover, &:focus {
					color: $white;
				}
			}
		}
	}
}

// Loader
.loader-portfolio-wrap {
	text-align: center;

	display: none;
	opacity: 0;
	visibility: hidden;
	left: 50%;
	top: 0px;
	transform: translateX(-50%);
	position: absolute;
}
.loader-portfolio, .site-loader {
  width: 2rem;
  height: 2rem;
  margin: 2rem auto;
  border-radius: 50%;
  border: 0.3rem solid rgba($white, 0.3);
  border-top-color: $white;
  animation: 1.5s spin infinite linear;
  &.dark {
  	border: 0.3rem solid rgba($black, 0.3);
  	border-top-color: $black;
  }
}



@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}


.jarallax {
  position: relative;
  z-index: 0;
}
.jarallax > .jarallax-img {
  position: absolute;
  object-fit: cover;
  /* support for plugin https://github.com/bfred-it/object-fit-images */
  font-family: 'object-fit: cover;';
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}


span.error {
	color: rgba($white, .5);
}

#message {
	resize: vertical;
}
#form-message-warning, #form-message-success {
	display: none;
}
#form-message-warning {
	color: #f64b3c;
}
#form-message-success {
	color: #8cba51;
	font-size: 18px;
	font-weight: bold;
}
.submitting {
	float: left;
	width: 100%;
	padding: 10px 0;
	display: none;
	font-weight: bold;
	font-size: 13px;
	letter-spacing: .1rem;
	color: rgba($white, .5);
}

// Sticky
.unslate_co--sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 80px;
  &:after, &:before {
  	content: '';
    display: table;
  }
}


.comment-form-wrap {
	clear: both;
}

.comment-list {
	padding: 0;
	margin: 0;
	.children {
		padding: 50px 0 0 40px;
		margin: 0;
		float: left;
		width: 100%;
	}

	li {
		padding: 0;
		margin: 0 0 30px 0;
		float: left;
		width: 100%;
		clear: both;
		list-style: none;
		.vcard {
			width: 80px;
			float: left;
			img {
				width: 50px;
				border-radius: 50%;
			}
		}
		.comment-body {
			float: right;
			width: calc(100% - 80px);
			h3 {
				font-size: 20px;
			}
			.meta {
				text-transform: uppercase;
				font-size: 13px;
				letter-spacing: .1em;
				color: #ccc;
				margin-bottom: 20px;
			}
			.reply {
				padding: 7px 15px;
				background: lighten($black, 50%);
				color: $white;
				text-transform: uppercase;
				border-radius: 30px;
				font-size: 11px;
				font-weight: 900;
				letter-spacing: .1rem;
				&:hover {
					color: $white;
					background: lighten($black, 55%);
				}
			}
		}
	}
}


.categories_tags {
	font-weight: bold;
}

.post-single-navigation {
	a {
		line-height: 1.5;
		font-weight: bold;
		span {
			margin-bottom: 10px;
			font-size: 11px;
			text-transform: uppercase;
			color: rgba($white, .4);
		}
		padding-top: 2rem;
		padding-bottom: 2rem;
		display: inline-block;
	}
	
}

.logo-v1 {

}


.lines-wrap {
	position: absolute;
	z-index: -1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  @include media-breakpoint-down(sm) {
  	display: none;
  }
  .lines-inner {
  	position: absolute;
    width: 1140px;
    left: 50%;
    margin-left: -570px;
    top: 0;
    height: 100%;
    border-left: 1px solid #222;
    border-right: 1px solid #222;
    .lines {
    	position: absolute;
	    left: 33.333333%;
	    right: 33.333333%;
	    height: 100%;
	    border-left: 1px solid #222;
	    border-right: 1px solid #222;
    }
  }
}

.gsap-reveal {
	position: relative;
	display: inline-block;
	overflow: hidden;
	&.gsap-reveal-filter {
		@include media-breakpoint-down(md) {
			overflow: visible!important;
		}
	}
	.cover {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba($black, 1);
	}
}

.gsap-reveal-hero {
	line-height: 0;
	.reveal-wrap {
		position: relative;
		overflow: hidden;
		display: inline-block;

		.cover {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba($primary, 1);
			margin-left: -100%;
			z-index: 2;
		}
		.reveal-content {
			line-height: 1.5;
			z-index: 1;
			display: inline-block;
			transform: translateX(-100%);
		}
	}
}

.gsap-reveal-img {


	line-height: 0;
	.reveal-wrap {
		position: relative;
		overflow: hidden;
		.cover {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba($primary, 1);
			margin-left: -100%;
			z-index: 9;
		}
		.reveal-content {
			opacity: 0;
			visibility: hidden;
			img {

				// transform: translateX(-102%);
			}

			.portfolio-item-content {
				h3 {
					margin-bottom: 10px;
				}
			}
		}
	}

}