<?php
/**
 * ISHTEEAHAR Digital Agency - Contact Page
 *
 * Contact form and company information
 */

// Include configuration and functions
require_once 'config/config.php';

// Handle form submission
$form_message = '';
$form_success = false;

if ($_POST && isset($_POST['csrf_token'])) {
    if (verifyCSRFToken($_POST['csrf_token'])) {
        $name = sanitizeInput($_POST['name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $subject = sanitizeInput($_POST['subject'] ?? '');
        $message = sanitizeInput($_POST['message'] ?? '');

        // Validation
        $errors = [];

        if (empty($name) || strlen($name) < 2) {
            $errors[] = 'Name is required and must be at least 2 characters.';
        }

        if (empty($email) || !isValidEmail($email)) {
            $errors[] = 'A valid email address is required.';
        }

        if (empty($message) || strlen($message) < 10) {
            $errors[] = 'Message is required and must be at least 10 characters.';
        }

        if (empty($errors)) {
            try {
                // Save to database
                $contact_data = [
                    'name' => $name,
                    'email' => $email,
                    'subject' => $subject ?: 'Contact Form Submission',
                    'message' => $message
                ];

                $message_id = saveContactMessage($contact_data);

                // Send email notification
                $email_subject = $subject ?: 'New Contact Form Submission';
                $email_message = "
                <h2>New Contact Form Submission</h2>
                <p><strong>Name:</strong> {$name}</p>
                <p><strong>Email:</strong> {$email}</p>

                <p><strong>Message:</strong></p>
                <p>{$message}</p>
                <hr>
                <p><small>This email was sent from the contact form on " . SITE_URL . "</small></p>
                ";

                $email_sent = sendEmailNotification(
                    getSetting('company_email', SITE_EMAIL),
                    $email_subject,
                    $email_message,
                    $name,
                    $email
                );

                if ($email_sent) {
                    $form_message = 'Thank you for your message! We will get back to you soon.';
                    $form_success = true;
                } else {
                    $form_message = 'Your message was saved but there was an issue sending the email notification.';
                    $form_success = true;
                }

            } catch (Exception $e) {
                $form_message = 'There was an error processing your message. Please try again.';
                logError($e->getMessage(), __FILE__, __LINE__);
            }
        } else {
            $form_message = implode('<br>', $errors);
        }
    } else {
        $form_message = 'Security token validation failed. Please try again.';
    }
}

// Set page-specific meta data
$meta = [
    'title' => 'Contact Us - ' . getSetting('site_title', SITE_NAME),
    'description' => 'Get in touch with ' . getSetting('company_name', SITE_NAME) . ' for your digital agency needs. We\'re here to help with web development, digital marketing, and creative solutions.',
    'keywords' => 'contact, digital agency, web development, digital marketing, get in touch, ISHTEEAHAR'
];

// Add special CSS for contact page
$additional_css = '
<style>
@import url("https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap");

/* Override body styles for contact page */
body.contact-page {
  font-family: "Quicksand", sans-serif;
  min-height: 100vh;
  overflow-x: hidden;
}


/* Contact Form Container */
.contact-form-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  margin-top: 100px;
}

.contact-form-content {
  position: relative;
  width: 100%;
  background: transparent;
  backdrop-filter: blur(15px);
  border-radius: 25px;
  padding: 50px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.3),
    0 0 0 2px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  background-clip: padding-box;
}

.contact-form-content::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ffd700, #ffed4e, #ffd700);
  border-radius: 27px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  0% { opacity: 0.6; }
  100% { opacity: 1; }
}

.contact-form-content h2 {
  font-size: 2.5em;
  color: #ffd700;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 40px;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

/* Form Styling */
.contact-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .form-group {
  flex: 1;
}

.inputBox {
  position: relative;
  width: 100%;
}

.inputBox input,
.inputBox textarea {
  position: relative;
  width: 100%;
  background: rgba(44, 82, 130, 0.3);
  border: 2px solid rgba(255, 215, 0, 0.3);
  outline: none;
  padding: 25px 15px 10px;
  border-radius: 10px;
  color: #fff;
  font-weight: 500;
  font-size: 1em;
  font-family: "Quicksand", sans-serif;
  transition: all 0.3s ease;
}

.inputBox input:focus,
.inputBox textarea:focus {
  border-color: #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.inputBox label {
  position: absolute;
  left: 15px;
  top: 20px;
  font-style: normal;
  color: #aaa;
  transition: 0.5s;
  pointer-events: none;
  font-family: "Quicksand", sans-serif;
}

.inputBox input:focus ~ label,
.inputBox input:valid ~ label,
.inputBox textarea:focus ~ label,
.inputBox textarea:valid ~ label {
  transform: translateY(-15px);
  font-size: 0.8em;
  color: #ffd700;
  background: rgba(26, 35, 50, 0.9);
  padding: 2px 8px;
  border-radius: 4px;
}

.submit-btn {
  padding: 15px 40px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  color: #000;
  font-weight: 700;
  font-size: 1.2em;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  transition: all 0.3s ease;
  font-family: "Quicksand", sans-serif;
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

.submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(255, 215, 0, 0.4);
  background: linear-gradient(45deg, #ffed4e, #ffd700);
}

.submit-btn:active {
  transform: translateY(-1px);
  opacity: 0.8;
}

/* Contact Info Styling */
.contact-info-section {
  background: transparent;
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  border: 2px solid rgba(255, 215, 0, 0.3);
  margin-top: 30px;
}

.contact-info-section h3 {
  color: #ffd700;
  font-size: 1.8em;
  margin-bottom: 30px;
  text-align: center;
}

.contact-info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.contact-info-item:hover {
  background: rgba(255, 215, 0, 0.2);
  transform: translateX(10px);
}

.contact-info-item i {
  color: #ffd700;
  font-size: 1.5em;
  margin-right: 15px;
  width: 30px;
}

/* Responsive Design */
@media (max-width: 900px) {
  .animated-bg-section span {
    width: calc(10vw - 2px);
    height: calc(10vw - 2px);
  }

  .form-row {
    flex-direction: column;
  }

  .contact-form-content {
    padding: 30px 20px;
  }
}

@media (max-width: 600px) {
  .animated-bg-section span {
    width: calc(20vw - 2px);
    height: calc(20vw - 2px);
  }

  .contact-form-content h2 {
    font-size: 2em;
  }
}
</style>';

// Include header
include 'includes/header.php';

// Add the additional CSS to the page
echo $additional_css;
?>

    <!-- Contact Form Container -->
    <div class="contact-form-container">
        <div class="contact-form-content">
            <h2><i class="icon-mail heading-icon glow"></i>Contact Us</h2>

            <?php if ($form_message): ?>
                <div class="alert <?php echo $form_success ? 'alert-success' : 'alert-danger'; ?> mb-4" style="background: rgba(<?php echo $form_success ? '40, 167, 69' : '220, 53, 69'; ?>, 0.2); border: 1px solid <?php echo $form_success ? '#28a745' : '#dc3545'; ?>; color: <?php echo $form_success ? '#28a745' : '#dc3545'; ?>; padding: 15px; border-radius: 10px; margin-bottom: 30px;">
                    <?php echo $form_message; ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <form method="post" class="contact-form" id="contactForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="form-row">
                            <div class="form-group">
                                <div class="inputBox">
                                    <input name="name" type="text" id="name" value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                                    <label for="name">Full Name *</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="inputBox">
                                    <input name="email" type="email" id="email" value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                    <label for="email">Email Address *</label>
                                </div>
                            </div>
                        </div>



                        <div class="inputBox">
                            <textarea name="message" id="message" rows="6" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            <label for="message">Your Message *</label>
                        </div>

                        <input type="submit" class="submit-btn" value="Send Message">
                    </form>
                </div>

                <div class="col-md-4">
                    <div class="contact-info-section">
                        <h3>Contact Information</h3>

                        <div class="contact-info-item">
                            <i class="icon-mail"></i>
                            <div>
                                <strong>Email</strong><br>
                                <a href="mailto:<?php echo getSetting('company_email', SITE_EMAIL); ?>" style="color: #ffd700;">
                                    <?php echo getSetting('company_email', SITE_EMAIL); ?>
                                </a>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <i class="icon-phone"></i>
                            <div>
                                <strong>Phone</strong><br>
                                <a href="tel:<?php echo getSetting('company_phone', '+****************'); ?>" style="color: #ffd700;">
                                    <?php echo getSetting('company_phone', '+****************'); ?>
                                </a>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <i class="icon-location"></i>
                            <div>
                                <strong>Address</strong><br>
                                <span style="color: #fff;">
                                    <?php echo nl2br(getSetting('company_address', '123 Digital Street<br>Tech City, TC 12345')); ?>
                                </span>
                            </div>
                        </div>

                        <div class="contact-info-item">
                            <i class="icon-clock"></i>
                            <div>
                                <strong>Business Hours</strong><br>
                                <span style="color: #fff;">
                                    Monday - Friday: 9:00 AM - 6:00 PM<br>
                                    Saturday: 10:00 AM - 4:00 PM<br>
                                    Sunday: Closed
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// Include footer
include 'includes/footer.php';
?>
